image: atlassian/default-image:4
definitions:
  services:
    docker:
      memory: 4096
  steps:
    - step: &build-image-push-harbor
        name: Build and Push to Harbor
        image: docker:24.0.6
        size: 2x
        services:
          - docker
        caches:
          - docker
        script:
          # Install dependencies
          - apk add --no-cache curl jq git
          
          # Force Docker to use the default driver
          - export DOCKER_BUILDKIT=0
          
          # Clean up any existing containers/images to free memory
          - docker system prune -f || true

          # Login to Harbor
          - echo $HARBOR_PASSWORD | docker login $HARBOR_URL -u $HARBOR_USERNAME --password-stdin

          # Build image with proper tags
          - git fetch --tags --unshallow || echo "Warning Could not fetch tags"
          - export CRYSTAL_FE_IMAGE_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v1.0.0")
          - export CRYSTAL_FE_FULL_IMAGE_NAME=$HARBOR_URL/$HARBOR_PROJECT/$HARBOR_PROJECT
          - echo "Building with CRYSTAL_FE_IMAGE_TAG $CRYSTAL_FE_IMAGE_TAG"
          - echo "Full image name $CRYSTAL_FE_FULL_IMAGE_NAME"

          # Verify Dockerfile exists
          - ls -la Dockerfile.dev

          # Build image with all environment variables as build args
          - >
            docker build
            --tag $CRYSTAL_FE_FULL_IMAGE_NAME:$CRYSTAL_FE_IMAGE_TAG
            --tag $CRYSTAL_FE_FULL_IMAGE_NAME:JP
            --tag $CRYSTAL_FE_FULL_IMAGE_NAME:latest
            --file Dockerfile.dev
            --memory=3g
            --memory-swap=3g
            --build-arg VITE_API_ENDPOINT="$VITE_API_ENDPOINT"
            --build-arg VITE_AWS_S3_ENDPOINT="$VITE_AWS_S3_ENDPOINT"
            --build-arg VITE_APP_NAME="$VITE_APP_NAME"
            --build-arg VITE_APP_FRONTEND="$VITE_APP_FRONTEND"
            --build-arg VITE_APP_VERSION="$VITE_APP_VERSION"
            --build-arg VITE_HOST="$VITE_HOST"
            --build-arg VITE_HOST_PORT="$VITE_HOST_PORT"
            --build-arg VITE_REVERB_APP_ID="$VITE_REVERB_APP_ID"
            --build-arg VITE_REVERB_APP_KEY="$VITE_REVERB_APP_KEY"
            --build-arg VITE_REVERB_APP_SECRET="$VITE_REVERB_APP_SECRET"
            --build-arg VITE_REVERB_HOST="$VITE_REVERB_HOST"
            --build-arg VITE_REVERB_WSPORT="$VITE_REVERB_WSPORT"
            --build-arg VITE_REVERB_WSSPORT="$VITE_REVERB_WSSPORT"
            --build-arg VITE_REVERB_SCHEME="$VITE_REVERB_SCHEME"
            .
          
          # Clean up build cache after build
          - docker builder prune -f || true

          # Push images
          - docker push $CRYSTAL_FE_FULL_IMAGE_NAME:$CRYSTAL_FE_IMAGE_TAG
          - docker push $CRYSTAL_FE_FULL_IMAGE_NAME:JP
          - docker push $CRYSTAL_FE_FULL_IMAGE_NAME:latest

          # Generate deployment artifacts
          - mkdir -p artifacts
          - echo "CRYSTAL_FE_IMAGE_TAG=$CRYSTAL_FE_IMAGE_TAG" > artifacts/build.env
          - echo "CRYSTAL_FE_FULL_IMAGE_NAME=$CRYSTAL_FE_FULL_IMAGE_NAME:$CRYSTAL_FE_IMAGE_TAG" >> artifacts/build.env
          - echo "BUILD_NUMBER=$BITBUCKET_BUILD_NUMBER" >> artifacts/build.env
          - echo "COMMIT_SHA=$BITBUCKET_COMMIT" >> artifacts/build.env
          - echo "CRYSTAL_FE_IMAGE_TAG=$CRYSTAL_FE_IMAGE_TAG" > artifacts/build.env
          - echo "CRYSTAL_FE_FULL_IMAGE_NAME=$CRYSTAL_FE_FULL_IMAGE_NAME:$CRYSTAL_FE_IMAGE_TAG" >> artifacts/build.env
        artifacts:
          - artifacts/**

    - step: &deploy-to-k0s
        name: Deploy to K0s
        image: alpine:latest
        script:
          - apk add --no-cache wget gettext
          - wget https://dl.k8s.io/release/v1.27.3/bin/linux/amd64/kubectl -O /usr/local/bin/kubectl
          - chmod +x /usr/local/bin/kubectl
          - kubectl version --client
          - mkdir -p ~/.kube

          # Setup kubectl
          - echo $KUBE_CONFIG | base64 -d > ~/.kube/config || { echo "Failed to decode kube config" >&2; exit 1; }
          - kubectl config use-context climbs-development-config-public || { echo "Failed to set kubectl context" >&2; exit 1; }
          # Apply Kubernetes manifests
          # - source k0s-deployment-yamls/crystal-be-deployment-envs.txt
          - set -a
          - . artifacts/build.env
          - set +a
          - env
          - envsubst < k0s-deployment-yamls/crystal-fe-test-deployment.yaml | kubectl apply -f - || { echo "Failed to apply deployment" >&2; exit 1; }
          # Wait for deployment
          - kubectl rollout status deployment/crystal-fe -n $CRYSTAL_FE_K0S_NAMESPACE --timeout=300s || { echo "Deployment rollout failed" >&2; exit 1; }
          # Display deployment status
          - kubectl get pods -n $CRYSTAL_FE_K0S_NAMESPACE
          - kubectl get services -n $CRYSTAL_FE_K0S_NAMESPACE
          - echo "CRYSTAL_FE_K0S_NAMESPACE=$CRYSTAL_FE_K0S_NAMESPACE"
          - echo "HARBOR_URL=$HARBOR_URL"
pipelines:
  custom:
    build-image-push-harbor:
      - step: *build-image-push-harbor
      - step: *deploy-to-k0s
# pipelines:
#   branches:
#     staging:
#       - step: *build-image

# main:
#   - step: *build-image
#   - step: *security-scan

# tags:
#    "*.*.*-beta":
#     - step: *build-image
#     - step: *security-scan 

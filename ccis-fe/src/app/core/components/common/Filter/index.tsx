import { FC, ChangeEvent, useState, useRef, useEffect } from "react";
import Button from "../Button";
import { FiFilter } from "react-icons/fi";
import TextField from "@components/form/TextField";
import { FaSearch } from "react-icons/fa";

type Props = {
  search?: string;
  children?: React.ReactNode;
  isOpen?: boolean;
  onClose?: () => void;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  hideButton?: boolean;
  placeholder?: string;
  hideSearch?: boolean;
};

const Filter: FC<Props> = ({ onChange, children, isOpen, hideButton = false, hideSearch = false, placeholder = "" }) => {
  const [filter, setFilter] = useState<boolean>(isOpen ?? false);
  const [searchValue, setSearchValue] = useState<string>("");

  const filterRef = useRef<HTMLDivElement>(null); // ref for the dropdown

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e);
    }
    setSearchValue(e.target.value);
  };

  const handleFilter = () => {
    setFilter((prev) => !prev);
  };

  // Close filter when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setFilter(false);
      }
    }

    if (filter) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [filter]);

  return (
    <div className="flex gap-2 relative mx-2" ref={filterRef}>
      {!hideSearch && (
        <div className="flex flex-1 flex-row justify-end items-center">
          <TextField
            rightIcon={<FaSearch className="text-accent" />}
            placeholder={`Search ${placeholder}`}
            className="min-w-96"
            size="sm"
            variant="primary"
            value={searchValue}
            onChange={handleChange}
          />
        </div>
      )}

      {!hideButton && (
        <Button
          classNames="text-zinc-950 border border-zinc-200 btn rounded-l btn-sm"
          onClick={handleFilter}
          isSubmitting={filter}
        >
          <FiFilter size={15} />
        </Button>
      )}

      <div
        className={`bg-zinc-100 border border-zinc-200 rounded-3xl h-max absolute top-10 w-96 left-96 p-6 pb-8 transition duration-500 ${
          filter ? "opacity-100 z-[100]" : "opacity-0 -z-[100]"
        }`}
      >
        <div>{children}</div>

        <div className="w-full text-xs justify-end flex mt-4 gap-4">
          <div
            className="text-info flex items-center justify-center cursor-pointer px-4 border p-2 rounded"
            onClick={handleFilter}
          >
            Cancel
          </div>
        </div>
      </div>
    </div>
  );
};

export default Filter;

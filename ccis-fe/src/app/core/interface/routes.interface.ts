export type RouteItem = {
  name: string;
  id: string;
  path: string;
  component: (props: any) => any;
  guard?: any;
  role?: any;
  index?: boolean;
  roles?: UserRoles[];
  permissions?: PermissionType[];
  icon?: React.ElementType | null;
  iconActive?: any;
  hidden?: boolean;
  current?: boolean;
  isSecondaryItem?: boolean;
  props?: any;
  children?: RouteItem[];
  isSidebar?: boolean;
  conditions?: (user: any, globalSettings?: any[]) => boolean;
};

export enum UserTypes {
  admin = "user-admin",
  user = "user",
}

export enum UserRoles {
  admin = "super-admin",
  areaAdmin = "area-admin",
  uatadmin = "uat-admin",
  user = "user",
  marketing = "marketing",
  actuary = "actuary",
  sea = "sales-executive-assistant",
  cashier = "cashier",
  treasury = "treasury-officer",
  sales = "sales",
  incomingCashier = "incoming-cashier",
  outgoingCashier = "outgoing-cashier",
  outgoingAdmin = "outgoing-admin",
  chiefCashier = "chief-cashier",
  clifsaAdmin = "clifsa-admin",
  gam = "gam",
  adminSatellite = "admin-satellite",
  ioc = "Incoming and Outgoing Cashier",
  compliance = "compliance",
  underwriting = "underwriting",
  claims = "claims",
  incomingAdmin = "incoming-admin",
  actuaryAnalyst1 = "actuary-analyst-1",
  actuaryAssistant1 = "actuary-assistant-1",
  actuaryManager = "actuary-manager",
  avpLifeNonLife = "avp-for-life-and-nonlife",
  rnd = "research-and-development",
  claimsManager = "claims-manager",
  vicePresidentSales = "vice-president-for-sales",
  vicePresidentOperations = "vice-president-for-operation",
  presidentCeo = "president-and-ceo",
  areaSalesManager = "area-sales-manager",
  infraOfficer = "infrastructure-officer",
  accounting = "accounting",
  //
  propertyCustodian = "property-custodian",
  regionalSalesManager = "regional-sales-manager",
  vpAgencyDistributionChannelManagement = "vp-agency-distribution-channel-management",
  dataProcessingAssistant = "data-processing-assistant",
  dataProcessingAssistant1 = "data-processing-assistant-1",
  maintenance = "maintenance",
  frontEndProgrammer = "front-end-programmer",
  administrativeAssistant = "administrative-assistant",
  managerAgencyDistributionChannel = "manager-agency-distribution-channel",
  avpAdminCorplan = "avp-admin-corplan",
  bookkeeper = "bookkeeper",
  adminSpecialist = "admin-specialist",
  claimsAssistant = "claims-assistant",
  uiUxDesigner = "ui-ux-designer",
  lifeCashier = "life-cashier",
  policyIssuanceAssistant = "policy-issuance-assistant",
  backEndProgrammer = "back-end-programmer",
  nlPropertyClaimsAssistant1 = "nl-property-claims-assistant-1",
  filingClerk = "filing-clerk",
  bookkeeperNonlife = "bookkeeper-nonlife",
  underwritingStaff = "underwriting-staff",
  dataEncoder = "data-encoder",
  salesDevelopmentOfficerNonlife = "sales-development-officer-nonlife",
  businessDevelopmentManager = "business-development-manager",
  memberRelationsAssistant2Life = "member-relations-assistant-2-life",
  assistantCashier1 = "assistant-cashier-1",
  treasuryOfficer = "treasury-officer",
  vpFinanceInvestmentTreasuryCompliance = "vp-finance-investment-treasury-compliance",
  claimsAssistant1Life = "claims-assistant-1-life",
  adminAssistantVpFinance = "admin-assistant-vp-finance",
  businessAnalyst = "business-analyst",
  dataProcessingManager = "data-processing-manager",
  oicOperationsManagerNonLife = "oic-operations-manager-non-life",
  oicSalesDevelopmentSpecialistVisayas = "oic-sales-development-specialist-visayas",
  claimsProcessor = "claims-processor",
  actuaryAnalyst = "actuary-analyst",
  technicalSupport = "technical-support",
  hrAssistant = "hr-assistant",
  internalAuditor = "internal-auditor",
  corporatePlanningAssistant1 = "corporate-planning-assistant-1",
  subsidiaryProjectAccountant = "subsidiary-project-accountant",
  claimsAssistant2 = "claims-assistant-2",
  disbursementAssistant = "disbursement-assistant",
  complianceManagerLife = "compliance-manager-life",
  claimsAdminAsst = "claims-admin-asst",
  dataWarehousingManager = "data-warehousing-manager",
  salesDevelopmentAnalystNcrCentralLuzon = "sales-development-analyst-ncr-central-luzon",
  underwritingAssistant1 = "underwriting-assistant-1",
  oicBookkeeperTiano = "oic-bookkeeper-tiano",
  adminAssistant1VpAdminCorplan = "admin-assistant-1-vp-admin-corplan",
  reinsuranceSpecialist = "reinsurance-specialist",
  //
  communicationsAssistant = "communications-assistant",
  bankReconciliationAssistant1Life = "bank-reconciliation-assistant-1-life",
  adminAsstOutgoing = "admin-asst-outgoing",
  oicAsm = "oic-asm",
  accountingAssistant = "accounting-assistant",
  technicalWriter = "technical-writer",
  underwritingAssistant = "underwriting-assistant",
  salesDevelopmentAnalystSouthLuzon = "sales-development-analyst-south-luzon",
  driver = "driver",
  accountant = "accountant",
  vpSalesMarketing = "vp-sales-marketing",
  socialMediaAssistant = "social-media-assistant",
  administrativeAssistantOperationsLife = "administrative-assistant-operations-life",
  vpAdminCorplanCeoPrincipalCcp = "vp-admin-corplan-ceo-principal-ccp",
  fireMarshall = "fire-marshall",
  messengerUtilityStaff = "messenger-utility-staff",
  bordereaux = "bordereaux",
  bankReconciliationAssistant1 = "bank-reconciliation-assistant-1",
  nafecoopBusinessDevtAssistant = "nafecoop-business-devt-assistant",
  qualityDataProcessorRetrievalTechnicalSupport = "quality-data-processor-retrieval-technical-support",
  accountingAssistant1 = "accounting-assistant-1",
  qaDocumentationAnalyst1 = "qa-documentation-analyst-1",
  actuaryAnalyst2 = "actuary-analyst-2",
  avpInvestmentTreasury = "avp-investment-treasury",
  collectionAnalyst = "collection-analyst",
  digitalMediaAssistantReliever = "digital-media-assistant-reliever",
  investmentAssistant = "investment-assistant",
  learningProgramCoordinator = "learning-program-coordinator",
  qualityAssuranceDocumentationAssistant1 = "quality-assurance-documentation-assistant-1",
  businessDevelopmentJuniorManager = "business-development-junior-manager",
  avpNlSalesLuzon = "avp-nl-sales-luzon",
  executiveManager = "executive-manager",
  underwriter = "underwriter",
  adminAsstCashier = "admin-asst-cashier",
  administrativeAssistantProcurement = "administrative-assistant-procurement",
  policyIssuanceAsst = "policy-issuance-asst",
  oicUnderwritingOfficer = "oic-underwriting-officer",
  systemProgrammer = "system-programmer",
  treasuryAssistant1 = "treasury-assistant-1",
  bookkeeperNl = "bookkeeper-nl",
  networkAdministrator = "network-administrator",
  claimsAssistant2Microinsurance = "claims-assistant-2-microinsurance",
  cashieringAssistant = "cashiering-assistant",
  avpSalesLife = "avp-sales-life",
  claimsExaminer = "claims-examiner",
  marketingAssistant = "marketing-assistant",
  //
  oicCompensationBenefitsAnalyst1 = "oic-compensation-benefits-analyst-1",
  operationsManager = "operations-manager",
  insurtechSeniorManager = "insurtech-senior-manager",
  memberRelationsAssistant = "member-relations-assistant",
  avpNonlifeSalesVismin = "avp-nonlife-sales-vismin",
  claimsManagerLife = "claims-manager-life",
  salesDevelopmentAnalystSouthMindanao = "sales-development-analyst-south-mindanao",
  nlReinsurance2 = "nl-reinsurance-2",
  systemDevelopmentSystemAdministrationManager = "system-development-system-administration-manager",
  legalCounselInHouse = "legal-counsel-in-house",
  avpLifeNonlife = "avp-life-nonlife",
  juniorProgrammer1 = "junior-programmer-1",
  policyIssuanceNl = "policy-issuance-nl",
  policyIssuanceClerk = "policy-issuance-clerk",
  complianceManagerNonlife = "compliance-manager-nonlife",
  infrastructureDataCenterManager = "infrastructure-data-center-manager",
  digitalMediaAssistant = "digital-media-assistant",
  programManager = "program-manager",
  dataWarehousingAssistant = "data-warehousing-assistant",
  dataQualityManagementSpecialist = "data-quality-management-specialist",
  claimsSpecialist = "claims-specialist",
  nonLifeClaimsClerk = "non-life-claims-clerk",
  hrManager = "hr-manager",
  bookkeeper1 = "bookkeeper-1",
  actuarialAssistant = "actuarial-assistant",
  avpFinance = "avp-finance",
  actuaryAssistantReliever = "actuary-assistant-reliever",
  customerServiceAcctRetentionSpecialist = "customer-service-acct-retention-specialist",
  reinsuranceAsst = "reinsurance-asst",
  assistantDivisionManagerNl = "assistant-division-manager-nl",
  claimsEvaluatorProcessor = "claims-evaluator-processor",
  memberRelationsManager = "member-relations-manager",
  underwritingFilingClerkClimbsCaresStaff = "underwriting-filing-clerk-climbs-cares-staff",
  administrativeAssistant1Oop = "administrative-assistant-1-oop",
  occupationalHealthNurse = "occupational-health-nurse",
  paralegal = "paralegal",
  adminEnvironmentalAssistant1 = "admin-environmental-assistant-1",
  chiefInternalAuditor = "chief-internal-auditor",
  reinsuranceUnderwritingManager = "reinsurance-underwriting-manager",
  projectResearchMelOfficer = "project-research-mel-officer",
  billingCollections = "billing-collections",
  //
  cashierNl = "cashier-nl",
  salesDevelopmentSpecialist = "sales-development-specialist",
  bankReconSpecialist = "bank-recon-specialist",
  chiefOfStaff = "chief-of-staff",
  underwritingAssistantNl = "underwriting-assistant-nl",
  dataProcessor = "data-processor",
  adminOfficer = "admin-officer",
  claimsAssistantNl = "claims-assistant-nl",
  claimsAnalyst = "claims-analyst",
}

export enum PermissionType {
  TEST_PERMISSION = "test_permission",
  SHARES_CREATE = "shares.create",
  SHARES_EDIT = "shares.update",
  SHARES_DELETE = "shares.delete",
  SHARES_VIEW = "shares.view",
  SHARES_PAYMENT_CREATE = "share_payments.create",
  SHARES_PAYMENT_EDIT = "share_payments.update",
  SHARES_PAYMENT_DELETE = "shares_payments.delete",
  SHARES_PAYMENT_VIEW = "shares_payments.view",
  PRODUCT_TYPE_VIEW = "product_types.view",
  PRODUCT_TYPE_CREATE = "product_types.create",
  PRODUCT_TYPE_UPDATE = "product_types.update",
  PRODUCT_TYPE_DELETE = "product_types.delete",
  //requirement Permission
  REQUIREMENT_TEMPLATE_VIEW = "requirement_templates.view",
  REQUIREMENT_TEMPLATE_CREATE = "requirement_templates.create",
  REQUIREMENT_TEMPLATE_UPDATE = "requirement_templates.update",
  REQUIREMENT_TEMPLATE_DELETE = "requirement_templates.delete",
  REQUIREMENT_CREATE = "requirement.create",
  REQUIREMENT_UPDATE = "requirement.update",
  REQUIREMENT_DELETE = "requirement.delete",
  REQUIREMENT_VIEW = "requirement.view",
  //cooperatives types Permission
  COOPERATIVES_TYPE_CREATE = "cooperative_types.create",
  COOPERATIVES_TYPE_UPDATE = "cooperative_types.update",
  COOPERATIVES_TYPE_DELETE = "cooperative_types.delete",
  COOPERATIVES_TYPE_VIEW = "cooperative_types.view",

  //cooperative Membership type Permission
  COOPERATIVES_MEMBERSHIP_TYPE_VIEW = "cooperative_membership_types.view",
  COOPERATIVES_MEMBERSHIP_TYPE_CREATE = "cooperative_membership_types.create",
  COOPERATIVES_MEMBERSHIP_TYPE_UPDATE = "cooperative_membership_types.update",
  COOPERATIVES_MEMBERSHIP_TYPE_DELETE = "cooperative_membership_types.delete",

  PRODUCT_GUIDELINES_SUBMIT_FOR_APPROVAL = "product.submit_for_approval",
  PRODUCT_REVISION_GUIDELINES_SUBMIT_FOR_APPROVAL = "productRevision.submit_for_approval",

  //Product Proposal
  PRODUCT_PROPOSAL_VIEW = "product_proposal.view",
  PRODUCT_PROPOSAL_CREATE = "product_proposal.create",
  PRODUCT_PROPOSAL_UPDATE = "product_proposal.update",
  PRODUCT_PROPOSAL_DELETE = "product_proposal.delete",
}

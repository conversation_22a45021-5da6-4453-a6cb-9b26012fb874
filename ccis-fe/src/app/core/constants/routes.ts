import { UserRoles } from "@interface/routes.interface";
// import { issuance } from "@services/routes/underwriting-routes";
export const adminBasePath = "/admin";
export const authBasePath = "/auth";
export const userBasePath = "/user";
export const uatUserBasePath = "/uat";
export const uatAdminBasePath = "/uat-admin";
export const marketingBasePath = "/marketing";
export const actuaryBasePath = "/actuary";
export const cashierBasePath = "/cashier";
export const salesBasePath = "/sales";
export const treasuryBasePath = "/treasury";
export const salesExecutiveAssistantBasePath = "/sales-executive-assistant";
export const complianceBasePath = "/compliance";
export const incomingCashierBasePath = "/incoming-cashier";
export const outgoingCashierBasePath = "/outgoing-cashier";
export const incomingAdminBasePath = "/incoming-admin";
export const outgoingAdminBasePath = "/outgoing-admin";
export const chiefCashierBasePath = "/chief-cashier";
export const clifsaAdminBasePath = "/clifsa-admin";
export const areaAdminBasePath = "/area-admin";
export const gamBasePath = "/gam";
export const adminSatelliteBasePath = "/admin-satellite";
export const cacBasePath = "/cac";
export const incomingOutgoingCashierBasePath = "/incoming-outgoing-cashier";
export const underwritingBasePath = "/underwriting";
export const claimsBasePath = "/claims";
export const actuaryAnalyst1BasePath = "/" + UserRoles.actuaryAnalyst1;
export const actuaryAssistant1BasePath = "/" + UserRoles.actuaryAssistant1;
export const actuaryManagerBasePath = "/" + UserRoles.actuaryManager;
export const avpLifeNonLifeManagerBasePath = "/" + UserRoles.avpLifeNonLife;
export const researchAndDevelopmentBasePath = "/" + UserRoles.rnd;
export const claimsManagerBasePath = "/" + UserRoles.claimsManager;
export const vicePresidentSalesBasePath = "/" + UserRoles.vicePresidentSales;
export const vicePresidentOperationsBasePath = "/" + UserRoles.vicePresidentOperations;
export const presidentCeoBasePath = "/" + UserRoles.presidentCeo;
export const areaSalesManagerBasePath = "/" + UserRoles.areaSalesManager;
export const infraOfficerBasePath = "/" + UserRoles.infraOfficer;
export const accountingBasePath = "/" + UserRoles.accounting;
export const propertyCustodianBasePath = "/" + UserRoles.propertyCustodian;
export const regionalSalesManagerBasePath = "/" + UserRoles.regionalSalesManager;
export const vpAgencyDistributionChannelManagementBasePath = "/" + UserRoles.vpAgencyDistributionChannelManagement;
export const dataProcessingAssistantBasePath = "/" + UserRoles.dataProcessingAssistant;
export const dataProcessingAssistant1BasePath = "/" + UserRoles.dataProcessingAssistant1;
export const maintenanceBasePath = "/" + UserRoles.maintenance;
export const frontEndProgrammerBasePath = "/" + UserRoles.frontEndProgrammer;
export const administrativeAssistantBasePath = "/" + UserRoles.administrativeAssistant;
export const managerAgencyDistributionChannelBasePath = "/" + UserRoles.managerAgencyDistributionChannel;
export const avpAdminCorplanBasePath = "/" + UserRoles.avpAdminCorplan;
export const bookkeeperBasePath = "/" + UserRoles.bookkeeper;
export const adminSpecialistBasePath = "/" + UserRoles.adminSpecialist;
export const claimsAssistantBasePath = "/" + UserRoles.claimsAssistant;
export const uiUxDesignerBasePath = "/" + UserRoles.uiUxDesigner;
export const lifeCashierBasePath = "/" + UserRoles.lifeCashier;
export const policyIssuanceAssistantBasePath = "/" + UserRoles.policyIssuanceAssistant;
export const backEndProgrammerBasePath = "/" + UserRoles.backEndProgrammer;
export const nlPropertyClaimsAssistant1BasePath = "/" + UserRoles.nlPropertyClaimsAssistant1;
export const filingClerkBasePath = "/" + UserRoles.filingClerk;
export const bookkeeperNonlifeBasePath = "/" + UserRoles.bookkeeperNonlife;
export const underwritingStaffBasePath = "/" + UserRoles.underwritingStaff;
export const dataEncoderBasePath = "/" + UserRoles.dataEncoder;
export const salesDevelopmentOfficerNonlifeBasePath = "/" + UserRoles.salesDevelopmentOfficerNonlife;
export const businessDevelopmentManagerBasePath = "/" + UserRoles.businessDevelopmentManager;
export const memberRelationsAssistant2LifeBasePath = "/" + UserRoles.memberRelationsAssistant2Life;
export const assistantCashier1BasePath = "/" + UserRoles.assistantCashier1;
export const treasuryOfficerBasePath = "/" + UserRoles.treasuryOfficer;
export const vpFinanceInvestmentTreasuryComplianceBasePath = "/" + UserRoles.vpFinanceInvestmentTreasuryCompliance;
export const claimsAssistant1LifeBasePath = "/" + UserRoles.claimsAssistant1Life;
export const adminAssistantVpFinanceBasePath = "/" + UserRoles.adminAssistantVpFinance;
export const businessAnalystBasePath = "/" + UserRoles.businessAnalyst;
export const dataProcessingManagerBasePath = "/" + UserRoles.dataProcessingManager;
export const oicOperationsManagerNonLifeBasePath = "/" + UserRoles.oicOperationsManagerNonLife;
export const oicSalesDevelopmentSpecialistVisayasBasePath = "/" + UserRoles.oicSalesDevelopmentSpecialistVisayas;
export const claimsProcessorBasePath = "/" + UserRoles.claimsProcessor;
export const actuaryAnalystBasePath = "/" + UserRoles.actuaryAnalyst;
export const technicalSupportBasePath = "/" + UserRoles.technicalSupport;
export const hrAssistantBasePath = "/" + UserRoles.hrAssistant;
export const internalAuditorBasePath = "/" + UserRoles.internalAuditor;
export const corporatePlanningAssistant1BasePath = "/" + UserRoles.corporatePlanningAssistant1;
export const subsidiaryProjectAccountantBasePath = "/" + UserRoles.subsidiaryProjectAccountant;
export const claimsAssistant2BasePath = "/" + UserRoles.claimsAssistant2;
export const disbursementAssistantBasePath = "/" + UserRoles.disbursementAssistant;
export const complianceManagerLifeBasePath = "/" + UserRoles.complianceManagerLife;
export const claimsAdminAsstBasePath = "/" + UserRoles.claimsAdminAsst;
export const dataWarehousingManagerBasePath = "/" + UserRoles.dataWarehousingManager;
export const salesDevelopmentAnalystNcrCentralLuzonBasePath = "/" + UserRoles.salesDevelopmentAnalystNcrCentralLuzon;
export const underwritingAssistant1BasePath = "/" + UserRoles.underwritingAssistant1;
export const oicBookkeeperTianoBasePath = "/" + UserRoles.oicBookkeeperTiano;
export const adminAssistant1VpAdminCorplanBasePath = "/" + UserRoles.adminAssistant1VpAdminCorplan;
export const reinsuranceSpecialistBasePath = "/" + UserRoles.reinsuranceSpecialist;
export const communicationsAssistantBasePath = "/" + UserRoles.communicationsAssistant;
export const bankReconciliationAssistant1LifeBasePath = "/" + UserRoles.bankReconciliationAssistant1Life;
export const adminAsstOutgoingBasePath = "/" + UserRoles.adminAsstOutgoing;
export const oicAsmBasePath = "/" + UserRoles.oicAsm;
export const accountingAssistantBasePath = "/" + UserRoles.accountingAssistant;
export const technicalWriterBasePath = "/" + UserRoles.technicalWriter;
export const underwritingAssistantBasePath = "/" + UserRoles.underwritingAssistant;
export const salesDevelopmentAnalystSouthLuzonBasePath = "/" + UserRoles.salesDevelopmentAnalystSouthLuzon;
export const driverBasePath = "/" + UserRoles.driver;
export const accountantBasePath = "/" + UserRoles.accountant;
export const vpSalesMarketingBasePath = "/" + UserRoles.vpSalesMarketing;
export const socialMediaAssistantBasePath = "/" + UserRoles.socialMediaAssistant;
export const administrativeAssistantOperationsLifeBasePath = "/" + UserRoles.administrativeAssistantOperationsLife;
export const vpAdminCorplanCeoPrincipalCcpBasePath = "/" + UserRoles.vpAdminCorplanCeoPrincipalCcp;
export const fireMarshallBasePath = "/" + UserRoles.fireMarshall;
export const messengerUtilityStaffBasePath = "/" + UserRoles.messengerUtilityStaff;
export const bordereauxBasePath = "/" + UserRoles.bordereaux;
export const bankReconciliationAssistant1BasePath = "/" + UserRoles.bankReconciliationAssistant1;
export const nafecoopBusinessDevtAssistantBasePath = "/" + UserRoles.nafecoopBusinessDevtAssistant;
export const qualityDataProcessorRetrievalTechnicalSupportBasePath = "/" + UserRoles.qualityDataProcessorRetrievalTechnicalSupport;
export const accountingAssistant1BasePath = "/" + UserRoles.accountingAssistant1;
export const qaDocumentationAnalyst1BasePath = "/" + UserRoles.qaDocumentationAnalyst1;
export const actuaryAnalyst2BasePath = "/" + UserRoles.actuaryAnalyst2;
export const avpInvestmentTreasuryBasePath = "/" + UserRoles.avpInvestmentTreasury;
export const collectionAnalystBasePath = "/" + UserRoles.collectionAnalyst;
export const digitalMediaAssistantRelieverBasePath = "/" + UserRoles.digitalMediaAssistantReliever;
export const investmentAssistantBasePath = "/" + UserRoles.investmentAssistant;
export const learningProgramCoordinatorBasePath = "/" + UserRoles.learningProgramCoordinator;
export const qualityAssuranceDocumentationAssistant1BasePath = "/" + UserRoles.qualityAssuranceDocumentationAssistant1;
export const businessDevelopmentJuniorManagerBasePath = "/" + UserRoles.businessDevelopmentJuniorManager;
export const avpNlSalesLuzonBasePath = "/" + UserRoles.avpNlSalesLuzon;
export const executiveManagerBasePath = "/" + UserRoles.executiveManager;
export const underwriterBasePath = "/" + UserRoles.underwriter;
export const adminAsstCashierBasePath = "/" + UserRoles.adminAsstCashier;
export const administrativeAssistantProcurementBasePath = "/" + UserRoles.administrativeAssistantProcurement;
export const policyIssuanceAsstBasePath = "/" + UserRoles.policyIssuanceAsst;
export const oicUnderwritingOfficerBasePath = "/" + UserRoles.oicUnderwritingOfficer;
export const systemProgrammerBasePath = "/" + UserRoles.systemProgrammer;
export const treasuryAssistant1BasePath = "/" + UserRoles.treasuryAssistant1;
export const bookkeeperNlBasePath = "/" + UserRoles.bookkeeperNl;
export const networkAdministratorBasePath = "/" + UserRoles.networkAdministrator;
export const claimsAssistant2MicroinsuranceBasePath = "/" + UserRoles.claimsAssistant2Microinsurance;
export const cashieringAssistantBasePath = "/" + UserRoles.cashieringAssistant;
export const avpSalesLifeBasePath = "/" + UserRoles.avpSalesLife;
export const claimsExaminerBasePath = "/" + UserRoles.claimsExaminer;
export const marketingAssistantBasePath = "/" + UserRoles.marketingAssistant;
export const oicCompensationBenefitsAnalyst1BasePath = "/" + UserRoles.oicCompensationBenefitsAnalyst1;
export const operationsManagerBasePath = "/" + UserRoles.operationsManager;
export const insurtechSeniorManagerBasePath = "/" + UserRoles.insurtechSeniorManager;
export const memberRelationsAssistantBasePath = "/" + UserRoles.memberRelationsAssistant;
export const avpNonlifeSalesVisminBasePath = "/" + UserRoles.avpNonlifeSalesVismin;
export const claimsManagerLifeBasePath = "/" + UserRoles.claimsManagerLife;
export const salesDevelopmentAnalystSouthMindanaoBasePath = "/" + UserRoles.salesDevelopmentAnalystSouthMindanao;
export const nlReinsurance2BasePath = "/" + UserRoles.nlReinsurance2;
export const systemDevelopmentSystemAdministrationManagerBasePath = "/" + UserRoles.systemDevelopmentSystemAdministrationManager;
export const legalCounselInHouseBasePath = "/" + UserRoles.legalCounselInHouse;
export const avpLifeNonlifeBasePath = "/" + UserRoles.avpLifeNonlife;
export const juniorProgrammer1BasePath = "/" + UserRoles.juniorProgrammer1;
export const policyIssuanceNlBasePath = "/" + UserRoles.policyIssuanceNl;
export const policyIssuanceClerkBasePath = "/" + UserRoles.policyIssuanceClerk;
export const complianceManagerNonlifeBasePath = "/" + UserRoles.complianceManagerNonlife;
export const infrastructureDataCenterManagerBasePath = "/" + UserRoles.infrastructureDataCenterManager;
export const digitalMediaAssistantBasePath = "/" + UserRoles.digitalMediaAssistant;
export const programManagerBasePath = "/" + UserRoles.programManager;
export const dataWarehousingAssistantBasePath = "/" + UserRoles.dataWarehousingAssistant;
export const dataQualityManagementSpecialistBasePath = "/" + UserRoles.dataQualityManagementSpecialist;
export const claimsSpecialistBasePath = "/" + UserRoles.claimsSpecialist;
export const nonLifeClaimsClerkBasePath = "/" + UserRoles.nonLifeClaimsClerk;
export const hrManagerBasePath = "/" + UserRoles.hrManager;
export const bookkeeper1BasePath = "/" + UserRoles.bookkeeper1;
export const actuarialAssistantBasePath = "/" + UserRoles.actuarialAssistant;
export const avpFinanceBasePath = "/" + UserRoles.avpFinance;
export const actuaryAssistantRelieverBasePath = "/" + UserRoles.actuaryAssistantReliever;
export const customerServiceAcctRetentionSpecialistBasePath = "/" + UserRoles.customerServiceAcctRetentionSpecialist;
export const reinsuranceAsstBasePath = "/" + UserRoles.reinsuranceAsst;
export const assistantDivisionManagerNlBasePath = "/" + UserRoles.assistantDivisionManagerNl;
export const claimsEvaluatorProcessorBasePath = "/" + UserRoles.claimsEvaluatorProcessor;
export const memberRelationsManagerBasePath = "/" + UserRoles.memberRelationsManager;
export const underwritingFilingClerkClimbsCaresStaffBasePath = "/" + UserRoles.underwritingFilingClerkClimbsCaresStaff;
export const administrativeAssistant1OopBasePath = "/" + UserRoles.administrativeAssistant1Oop;
export const occupationalHealthNurseBasePath = "/" + UserRoles.occupationalHealthNurse;
export const paralegalBasePath = "/" + UserRoles.paralegal;
export const adminEnvironmentalAssistant1BasePath = "/" + UserRoles.adminEnvironmentalAssistant1;
export const chiefInternalAuditorBasePath = "/" + UserRoles.chiefInternalAuditor;
export const reinsuranceUnderwritingManagerBasePath = "/" + UserRoles.reinsuranceUnderwritingManager;
export const projectResearchMelOfficerBasePath = "/" + UserRoles.projectResearchMelOfficer;
export const billingCollectionsBasePath = "/" + UserRoles.billingCollections;
export const salesDevelopmentSpecialistBasePath = "/" + UserRoles.salesDevelopmentSpecialist;
export const bankReconSpecialistBasePath = "/" + UserRoles.bankReconSpecialist;
export const chiefOfStaffBasePath = "/" + UserRoles.chiefOfStaff;
export const underwritingAssistantNlBasePath = "/" + UserRoles.underwritingAssistantNl;
export const dataProcessorBasePath = "/" + UserRoles.dataProcessor;
export const adminOfficerBasePath = "/" + UserRoles.adminOfficer;
export const claimsAssistantNlBasePath = "/" + UserRoles.claimsAssistantNl;
export const claimsAnalystBasePath = "/" + UserRoles.claimsAnalyst;
export const cashierNlBasePath = "/" + UserRoles.cashierNl;
export const usersFeedbackBasePath = "/users-feedback";

export const ROUTES = {
  AUTH: {
    login: {
      key: authBasePath + "/",
    },
  },
  USERS_FEEDBACK: {
    surveyForm: {
      key: usersFeedbackBasePath,
    },
  },

  ADMIN: {
    notification: { key: adminBasePath + "/notification" },
    profile: {
      key: adminBasePath + "/profile",
    },
    dashboard: {
      key: adminBasePath + "/dashboard",
    },
    users: {
      key: adminBasePath + "/users-management",
    },
    roles: {
      key: adminBasePath + "/roles-management",
    },
    role: {
      key: adminBasePath + "/role-management",
    },
    productUtilities: {
      key: adminBasePath + "/product-utilities",
    },
    types: {
      key: adminBasePath + "/product-utilities/types",
    },
    category: {
      key: adminBasePath + "/product-utilities/category",
    },
    commissionType: {
      key: adminBasePath + "/product-utilities/commissionType",
    },
    commissionTypeAge: {
      key: adminBasePath + "/product-utilities/commissionTypeAge",
    },
    signatories: {
      key: adminBasePath + "/product-utilities/signatories",
    },
    signatoryType: {
      key: adminBasePath + "/product-utilities/signatoryType",
    },

    targetMarket: {
      key: adminBasePath + "/product-utilities/targetMarket",
    },
    headers: {
      key: adminBasePath + "/product-utilities/headers",
    },
    contestability: {
      key: adminBasePath + "/product-utilities/contestability",
    },
    systemUtilities: {
      key: adminBasePath + "/system-utilities",
    },
    departments: {
      key: adminBasePath + "/system-utilities/departments",
    },
    positions: {
      key: adminBasePath + "/system-utilities/positions",
    },
    userAreas: {
      key: adminBasePath + "/system-utilities/user-area",
    },
    products: {
      key: adminBasePath + "/products",
    },
    revisions: {
      key: adminBasePath + "/products/:productid/revisions",
      parse: (pid: string) => adminBasePath + `/products/${pid}/revisions`,
    },
    reviewRevisions: {
      key: adminBasePath + "/products/:productid/revision/:revisionid",
      parse: (pid: string, rid: string) => adminBasePath + `/products/${pid}/revision/${rid}`,
    },
    editProductDetails: {
      key: adminBasePath + "/product/details/:id",
      parse: (id: string) => adminBasePath + `/product/details/${id}`,
    },
    guidelines: {
      key: adminBasePath + "/products/guidelines",
    },
    createGuidelines: {
      key: adminBasePath + "/products/guidelines/create",
    },
    editGuidelines: {
      key: adminBasePath + "/products/guidelines/edit/:id",
      parse: (id: string) => adminBasePath + `/products/guidelines/edit/${id}`,
    },
    editRevision: {
      key: adminBasePath + "/products/revisions/edit/:id",
      parse: (id: string) => adminBasePath + `/products/revisions/edit/${id}`,
    },
    cloneProductGuideline: {
      key: adminBasePath + "/products/guidelines/clone",
    },
    cloneProduct: {
      key: adminBasePath + "/products/clone",
    },
    reviewProductRevisionsFromEmail: {
      key: adminBasePath + "/products/:productid/revision/:revisionid/review",
      parse: (pid: string, rid: string) => adminBasePath + `/products/${pid}/revision/${rid}/review`,
    },
    remittancesUtilities: {
      key: adminBasePath + "/remittances-utilities",
    },
    remittanceType: {
      key: adminBasePath + "/remittances-utilities/remittance-type",
    },
    remittanceData: {
      key: adminBasePath + "/remittances-utilities/remittance-data",
    },
    shares: {
      key: adminBasePath + "/shares",
    },
    sharescooperativesUtilities: {
      key: adminBasePath + "/shares-cooperatives-utilities",
    },
    affiliation: {
      key: adminBasePath + "/shares-cooperatives-utilities/affiliations",
    },
    cooperativeCategoryTable: {
      key: adminBasePath + "/shares-cooperatives-utilities/cooperative-category",
    },
    cooperativeType: {
      key: adminBasePath + "/shares-cooperatives-utilities/cooperative-type",
    },
    requirements: {
      key: adminBasePath + "/shares-cooperatives-utilities/requirements",
    },
    requirementsTemplate: {
      key: adminBasePath + "/shares-cooperatives-utilities/requirement-template",
    },
    cooperetiveMembership: {
      key: adminBasePath + "/shares-cooperatives-utilities/cooperative-membership",
    },
    productProposal: {
      key: adminBasePath + "/product-proposal",
    },
    viewProductProposalCustomize: {
      key: adminBasePath + "/product-proposal/:id",
      parse: (id: string) => adminBasePath + `/product-proposal/${id}`,
    },
    viewProductProposal: {
      key: adminBasePath + "/product-proposal/:id",
      parse: (id: string) => adminBasePath + `/product-proposal/${id}`,
    },
    createProductProposal: {
      key: adminBasePath + "/product-proposal/create",
    },
    editProductProposal: {
      key: adminBasePath + "/product-proposal/edit/:id",
      parse: (id: string) => adminBasePath + `/product-proposal/edit/${id}`,
    },
    // compliance: {
    //   key: adminBasePath + "/compliance",
    // },
    // viewCompliance: {
    //   key: adminBasePath + "/compliance/:id",
    //   parse: (id: string) => adminBasePath + `/compliance/${id}`,
    // },
    formInventoryUtilities: {
      key: adminBasePath + "/form-inventory-utilities",
    },
    formInventoryUtilitiesDivision: {
      key: adminBasePath + "/form-inventory-utilities/form-inventory-division",
    },
    formInventoryUtilitiesType: {
      key: adminBasePath + "/form-inventory-utilities/form-inventory-type",
    },
    globalSettings: {
      key: adminBasePath + "/global-settings",
    },
    proposalSettings: {
      key: adminBasePath + "/global-settings/proposal-settings",
    },
    divisions: {
      key: adminBasePath + "/system-utilities/divisions",
    },
    formTypes: {
      key: adminBasePath + "/form-inventory-utilities/form-types",
    },
    paymentMethods: {
      key: adminBasePath + "/form-inventory-utilities/payment-methods",
    },
    banks: {
      key: adminBasePath + "/form-inventory-utilities/banks",
    },
    incomingOutgoingCashierDashboard: {
      key: adminBasePath + "/incoming-outgoing-cashier-dashboard",
    },
    marketAreas: {
      key: adminBasePath + "/form-inventory-utilities/market-areas",
    },
    bankAccounts: {
      key: adminBasePath + "/form-inventory-utilities/bank-accounts",
    },
    releasedMethods: {
      key: adminBasePath + "/form-inventory-utilities/released-methods",
    },
    regions: {
      key: adminBasePath + "/form-inventory-utilities/regions",
    },
    requestDashboard: {
      key: adminBasePath + "/request-dashboard",
    },
    requestForm: {
      key: adminBasePath + "/request-form",
    },
    viewRequestForm: {
      key: adminBasePath + "/request-form/:id",
      parse: (id: string) => adminBasePath + `/request-form/${id}`,
    },
    ticketUtilities: {
      key: adminBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: adminBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: adminBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: adminBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: adminBasePath + "/ticket-utilities/devices-system",
    },
    formInventorylSettings: {
      key: adminBasePath + "/global-settings/form-inventory-settings",
    },
    quotationFipAerView: {
      key: adminBasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => adminBasePath + "/quotations/aer-view/fip/" + `${id}`,
    },
    myApprovals: {
      key: adminBasePath + "/my-approvals",
    },
    aerApproval: {
      key: adminBasePath + "/my-approvals/aer-approvals",
    },

    quotationClspQuotationView: {
      key: adminBasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => adminBasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: adminBasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => adminBasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: adminBasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => adminBasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },
    commissionAndRequirements: {
      key: adminBasePath + "/my-approvals/validation/commission-and-requirements",
    },
    viewProductProposalSignatory: {
      key: adminBasePath + "/approvals/validation/commission-and-requirements/view/:id",
      parse: (id: string) => adminBasePath + `/approvals/validation/commission-and-requirements/view/${id}`,
    },
    weeklyAccomplishmentReport: {
      key: adminBasePath + "/weekly-accomplishment-report",
    },
    managerWeeklyAccomplishmentDashboard: {
      key: adminBasePath + "/manager-weekly-accomplishment-dashboard",
    },
    employeeWeeklyAccomplishmentDashboard: {
      key: adminBasePath + "/employee-weekly-accomplishment-dashboard",
    },
    system_settings: {
      key: adminBasePath + "/global-settings/system-settings",
    },
    surveyDashboard: {
      key: adminBasePath + "/survey-dashboard",
    },
  },

  USERS: {
    notification: { key: userBasePath + "/notification" },
    dashboard: {
      key: userBasePath + "/dashboard",
    },
    approvals: {
      key: userBasePath + "/approvals",
    },
    reviewProductRevisions: {
      key: userBasePath + "/approvals/review/product/:productid/revision/:revisionid",
      parse: (pid: string, rid: string) => userBasePath + `/approvals/review/product/${pid}/revision/${rid}`,
    },
    profile: {
      key: userBasePath + "/profile",
    },
    reviewProductRevisionsFromEmail: {
      key: "/products/:productid/revision/:revisionid/review",
      parse: (pid: string, rid: string) => `/products/${pid}/revision/${rid}/review`,
    },
    requestDashboard: {
      key: adminBasePath + "/request-dashboard",
    },
    requestForm: {
      key: adminBasePath + "/request-form",
    },
    viewRequestForm: {
      key: adminBasePath + "/request-form/:id",
      parse: (id: string) => adminBasePath + `/request-form/${id}`,
    },
  },
  UATUSERS: {
    notification: { key: uatUserBasePath + "/notification" },
    uatUser: {
      key: uatUserBasePath + "/uat-sheet",
    },
    profile: {
      key: uatUserBasePath + "/profile",
    },
  },
  UATADMIN: {
    uatManagement: {
      key: uatAdminBasePath + "/uat-management",
    },
    uatResults: {
      key: uatAdminBasePath + "/uat-results",
    },
    notification: { key: uatAdminBasePath + "/notification" },
    profile: {
      key: uatAdminBasePath + "/profile",
    },
  },
  MARKETING: {
    notification: { key: marketingBasePath + "/notification" },
    marketingDashboard: {
      key: marketingBasePath + "/",
    },
    validation: {
      key: marketingBasePath + "/validation",
    },
    commissionAndRequirements: {
      key: marketingBasePath + "/validation/commission-and-requirements",
    },
    viewProductProposal: {
      key: marketingBasePath + "/validation/commission-and-requirements/view/:id",
      parse: (id: string) => marketingBasePath + `/validation/commission-and-requirements/view/${id}`,
    },
    shares: {
      key: marketingBasePath + "/validation/shares",
    },
    profile: {
      key: marketingBasePath + "/profile",
    },
    productProposal: {
      key: marketingBasePath + "/product-proposal",
    },
    viewProductProposalCustomize: {
      key: marketingBasePath + "/product-proposal/:id",
      parse: (id: string) => marketingBasePath + `/product-proposal/${id}`,
    },
    viewProductProposalSales: {
      key: marketingBasePath + "/product-proposal/:id",
      parse: (id: string) => marketingBasePath + `/product-proposal/${id}`,
    },
    requestDashboard: {
      key: marketingBasePath + "/request-dashboard",
    },
    requestForm: {
      key: marketingBasePath + "/request-form",
    },
    viewRequestForm: {
      key: marketingBasePath + "/request-form/:id",
      parse: (id: string) => marketingBasePath + `/request-form/${id}`,
    },
  },
  SALESEXECUTIVEASSISTANT: {
    notification: { key: salesExecutiveAssistantBasePath + "/notification" },
    salesExecutiveAssistantDashboard: {
      key: salesExecutiveAssistantBasePath + "/dashboard",
    },
    notarization: {
      key: salesExecutiveAssistantBasePath + "/product-proposals-notarization",
    },
    partnershipAgreementNotary: {
      key: salesExecutiveAssistantBasePath + "/product-proposals-notarization/partnership-agreement/:id",
      parse: (id: string) => salesExecutiveAssistantBasePath + `/product-proposals-notarization/partnership-agreement/${id}`,
    },
    productProposal: {
      key: salesExecutiveAssistantBasePath + "/product-proposal",
    },
    profile: {
      key: salesExecutiveAssistantBasePath + "/profile",
    },
  },

  ACTUARY: {
    notification: { key: actuaryBasePath + "/notification" },
    actuaryDashboard: {
      key: actuaryBasePath + "/dashboard",
    },
    AER: {
      key: actuaryBasePath + "/AER",
    },
    GYRT: {
      key: actuaryBasePath + "/AER/gyrt",
    },
    reviewAerGYRT: {
      key: actuaryBasePath + "/GYRT/review-aer",
    },
    createAerGYRT: {
      key: actuaryBasePath + "/AER/create-aer",
    },
    viewAerGYRT: {
      key: actuaryBasePath + "/GYRT/view-aer",
    },
    viewQoutationRequestGYRT: {
      key: actuaryBasePath + "/GYRT/view-qoutation-request/:id",
      parse: (id: string) => actuaryBasePath + `/GYRT/view-qoutation-request/${id}`,
    },
    signatoryGYRT: {
      key: actuaryBasePath + "/GYRT/create-aer/signatory",
    },
    benefits: {
      key: actuaryBasePath + "/product-utilities/benefits",
    },
    FIP: {
      key: actuaryBasePath + "/FIP",
    },
    createAerFIP: {
      key: actuaryBasePath + "/FIP/create-aer",
    },
    viewAerFIP: {
      key: actuaryBasePath + "/FIP/view-aer",
    },
    reviewAerFIP: {
      key: actuaryBasePath + "/FIP/review-aer",
    },
    viewQoutationRequestFIP: {
      key: actuaryBasePath + "/FIP/view-qoutation-request/:id",
      parse: (id: string) => actuaryBasePath + `/FIP/view-qoutation-request/${id}`,
    },
    signatoryFIP: {
      key: actuaryBasePath + "/FIP/create-aer/signatory",
    },
    CLSP: {
      key: actuaryBasePath + "/AER/CLSP",
    },
    viewCLSPAER: {
      key: actuaryBasePath + "/CLSP/view-aer",
    },
    viewQuotationRequestCLSP: {
      key: actuaryBasePath + "/CLSP/view-quotation-request",
    },
    createCLSPAER: {
      key: actuaryBasePath + "/CLSP/create-aer-qr",
    },
    createCLSPSignatory: {
      key: actuaryBasePath + "/clsp-signatory",
    },
    reviewCLSP: {
      key: actuaryBasePath + "/clsp/review",
    },
    utilities: {
      key: actuaryBasePath + "/utilities",
    },
    mortalityRate: {
      key: actuaryBasePath + "/utilities/mortality-rate",
    },
    adminExpense: {
      key: actuaryBasePath + "/utilities/admin-expense",
    },
    picRate: {
      key: actuaryBasePath + "/utilities/pic-rate",
    },
    riskPremiumRate: {
      key: actuaryBasePath + "/utilities/risk-premium-rate",
    },
    defaultNumberOfClaims: {
      key: actuaryBasePath + "/utilities/default-number-of-claims",
    },
    benefitRate: {
      key: actuaryBasePath + "/utilities/benefit-rate",
    },
    CLPP: {
      key: actuaryBasePath + "/AER/CLPP",
    },
    viewQuotationRequestCLPP: {
      key: actuaryBasePath + "/CLPP/view-quotation-request",
    },
    createClppAER: {
      key: actuaryBasePath + "/CLPP/create-aer",
    },
    createCLPPSignatory: {
      key: actuaryBasePath + "/clpp-signatory",
    },
    reviewCLPP: {
      key: actuaryBasePath + "/clpp/review",
    },
    profile: {
      key: actuaryBasePath + "/profile",
    },

    GYRTMortalities: {
      key: actuaryBasePath + "/utilities/mortality-rate/gyrt-mortality-rates/:type",
      parse: (type: string) => actuaryBasePath + `/utilities/mortality-rate/gyrt-mortality-rates/${type}`,
    },
    CLSPMortalies: {
      key: actuaryBasePath + "/utilities/mortality-rate/clsp-mortality-rates/:type",
      parse: (type: string) => actuaryBasePath + `/utilities/mortality-rate/clsp-mortality-rates/${type}`,
    },
    PICMortalities: {
      key: actuaryBasePath + "/utilities/mortality-rate/pic-mortality-rates",
    },

    clppRate: {
      key: actuaryBasePath + "/utilities/clpp-rate",
    },
    actuaryUtilities: {
      key: actuaryBasePath + "/utilities/actuary",
    },
    requestDashboard: {
      key: actuaryBasePath + "/request-dashboard",
    },
    requestForm: {
      key: actuaryBasePath + "/request-form",
    },
    viewRequestForm: {
      key: actuaryBasePath + "/request-form/:id",
      parse: (id: string) => actuaryBasePath + `/request-form/${id}`,
    },
    productProposal: {
      key: actuaryBasePath + "/product-proposal",
    },
    viewProductProposalCustomize: {
      key: actuaryBasePath + "/product-proposal/:id",
      parse: (id: string) => actuaryBasePath + `/product-proposal/${id}`,
    },
    viewProductProposal: {
      key: actuaryBasePath + "/product-proposal/:id",
      parse: (id: string) => actuaryBasePath + `/product-proposal/${id}`,
    },
    ticketUtilities: {
      key: actuaryBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: actuaryBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: actuaryBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: actuaryBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: actuaryBasePath + "/ticket-utilities/devices-system",
    },
  },
  CASHIER: {
    notification: { key: cashierBasePath + "/notification" },
    actuaryDashboard: {
      key: cashierBasePath + "/dashboard",
    },
    cashierNewForm: {
      key: cashierBasePath + "/si-or-forms",
    },
    hqCashierNewForm: {
      key: cashierBasePath + "/si-or-pr-forms",
    },
    prReceiving: {
      key: cashierBasePath + "/pr-forms",
    },
    viewForReceivingForm: {
      key: cashierBasePath + "/for-receiving-forms/view/:id",
      parse: (id: string) => cashierBasePath + `/for-receiving-forms/view/${id}`,
    },
    viewReleaseTransmittalForm: {
      key: cashierBasePath + "/used-forms/view/:id",
      parse: (id: string) => cashierBasePath + `/used-forms/view/${id}`,
    },
    requestPads: {
      key: cashierBasePath + "/request-pads",
    },
    inventory: {
      key: cashierBasePath + "/inventory",
    },
    shares: {
      key: cashierBasePath + "/shares",
    },
    profile: {
      key: cashierBasePath + "/profile",
    },
  },
  CAC: {
    notification: { key: cacBasePath + "/notification" },
    cacDashboard: {
      key: cacBasePath + "/dashboard",
    },
    profile: {
      key: cacBasePath + "/profile",
    },adminSatelliteForReceivingForms: {
      key: adminSatelliteBasePath + "/for-receiving-forms",
    },
    adminSatelliteAdminNewForm: {
      key: adminSatelliteBasePath + "/new-forms",
    },
    adminSatelliteInventory: {
      key: adminSatelliteBasePath + "/inventory",
    },
    viewForReceivingForm: {
      key: adminSatelliteBasePath + "/for-receiving-forms/view/:id",
      parse: (id: string) => adminSatelliteBasePath + `/for-receiving-forms/view/${id}`,
    },
    viewFormReceiving: {
      key: adminSatelliteBasePath + "/new-forms/view/:id",
      parse: (id: string) => adminSatelliteBasePath + `/new-forms/view/${id}`,
    },
    viewReturnedForm: {
      key: adminSatelliteBasePath + "/returned-form/:id",
      parse: (id: string) => adminSatelliteBasePath + `/returned-form/${id}`,
    },
    requestPads: {
      key: adminSatelliteBasePath + "/request-pads",
    },
    transmittalReturnedForm: {
      key: adminSatelliteBasePath + "/return-transmittal",
    },
    viewPrTable: {
      key: adminSatelliteBasePath + "/pr-table/:id",
      parse: (id: string) => adminSatelliteBasePath + `/pr-table/${id}`,
    },
    issuePRForm: {
      key: adminSatelliteBasePath + "/issue-pr-form/:id",
      parse: (id: string) => adminSatelliteBasePath + `/issue-pr-form/${id}`,
    },
    viewPR: {
      key: adminSatelliteBasePath + "/view-pr/:id",
      parse: (id: string) => adminSatelliteBasePath + `/view-pr/${id}`,
    },
  },

  SALES: {
    notification: { key: salesBasePath + "/notification" },
    salesDashboard: {
      key: salesBasePath + "/dashboard",
    },
    shares: {
      key: salesBasePath + "/shares",
    },
    quotations: {
      key: salesBasePath + "/quotations",
    },
    quotationGyrtAerView: {
      key: salesBasePath + "/quotations/aer-view/gyrt/:id",
      parse: (id: string) => salesBasePath + "/quotations/aer-view/gyrt/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: salesBasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => salesBasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },

    quotationClppAerView: {
      key: salesBasePath + "/quotations/aer-view/clpp/:id",
      parse: (id: string) => salesBasePath + "/quotations/aer-view/clpp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: salesBasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => salesBasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },

    quotationClspAerView: {
      key: salesBasePath + "/quotations/aer-view/clsp/:id",
      parse: (id: string) => salesBasePath + "/quotations/aer-view/clsp/" + `${id}`,
    },
    quotationClspQuotationView: {
      key: salesBasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => salesBasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },

    gyrtQuotation: {
      key: salesBasePath + "/quotations/gyrt/:id",
      parse: (id: string) => salesBasePath + "/quotations/gyrt/" + `${id}`,
    },
    clppQuotation: {
      key: salesBasePath + "/quotations/clpp/:id",
      parse: (id: string) => salesBasePath + "/quotations/clpp/" + `${id}`,
    },
    clspQuotation: {
      key: salesBasePath + "/quotations/clsp/:id",
      parse: (id: string) => salesBasePath + "/quotations/clsp/" + `${id}`,
    },
    fipQuotation: {
      key: salesBasePath + "/quotations/fip/:id",
      parse: (id: string) => salesBasePath + "/quotations/fip/" + `${id}`,
    },
    productProposal: {
      key: salesBasePath + "/product-proposal",
    },
    createProductProposal: {
      key: salesBasePath + "/product-proposal/create",
    },
    editProductProposal: {
      key: salesBasePath + "/product-proposal/edit/:id",
      parse: (id: string) => salesBasePath + `/product-proposal/edit/${id}`,
    },
    quotationFipAerView: {
      key: salesBasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => salesBasePath + "/quotations/aer-view/fip/" + `${id}`,
    },
    viewProductProposal: {
      key: salesBasePath + "/product-proposal/:id",
      parse: (id: string) => salesBasePath + `/product-proposal/${id}`,
    },
    requestDashboard: {
      key: salesBasePath + "/request-dashboard",
    },
    requestForm: {
      key: salesBasePath + "/request-form",
    },
    viewRequestForm: {
      key: salesBasePath + "/request-form/:id",
      parse: (id: string) => salesBasePath + `/request-form/${id}`,
    },
    profile: {
      key: salesBasePath + "/profile",
    },
  },

  TREASURY: {
    notification: { key: treasuryBasePath + "/notification" },
    treasuryDashboard: {
      key: treasuryBasePath + "/dashboard",
    },
    shares: {
      key: treasuryBasePath + "/shares",
    },
    newForms: {
      key: treasuryBasePath + "/new-forms",
    },
    viewForm: {
      key: treasuryBasePath + "/new-forms/view/:id",
      parse: (id: string) => treasuryBasePath + `/new-forms/view/${id}`,
    },
    requestPads: {
      key: treasuryBasePath + "/request-pads",
    },
    requestPadForm: {
      key: treasuryBasePath + "/request-pads/view/:id",
      parse: (id: string) => treasuryBasePath + `/request-pads/view/${id}`,
    },
    profile: {
      key: treasuryBasePath + "/profile",
    },
  },
  COMPLIANCE: {
    notification: { key: complianceBasePath + "/notification" },
    complianceDashboard: {
      key: complianceBasePath + "/dashboard",
    },
    compliance: {
      key: complianceBasePath + "/compliance",
    },
    viewCompliance: {
      key: complianceBasePath + "/compliance/:id",
      parse: (id: string) => complianceBasePath + `/compliance/${id}`,
    },
    profile: {
      key: complianceBasePath + "/profile",
    },
    requestDashboard: {
      key: complianceBasePath + "/request-dashboard",
    },
    requestForm: {
      key: complianceBasePath + "/request-form",
    },
    viewRequestForm: {
      key: complianceBasePath + "/request-form/:id",
      parse: (id: string) => complianceBasePath + `/request-form/${id}`,
    },
  },
  INCOMINGCASHIER: {
    notification: { key: incomingCashierBasePath + "/notification" },
    incomingCashierDashboard: {
      key: incomingCashierBasePath + "/dashboard",
    },
    profile: {
      key: incomingCashierBasePath + "/profile",
    },
  },
  OUTGOINGCASHIER: {
    notification: { key: outgoingCashierBasePath + "/notification" },
    outgoingCashierDashboard: {
      key: outgoingCashierBasePath + "/dashboard",
    },
    profile: {
      key: outgoingCashierBasePath + "/profile",
    },
  },
  OUTGOINGADMIN: {
    notification: { key: outgoingAdminBasePath + "/notification" },
    outgoingAdminDashboard: {
      key: outgoingAdminBasePath + "/dashboard",
    },
    outgoingAdminNewForms: {
      key: outgoingAdminBasePath + "/new-forms",
    },
    outgoingAdminNewFormsOutgoing: {
      key: outgoingAdminBasePath + "/new-forms/outgoing",
    },
    forOutgoingAdminReceivingForm: {
      key: outgoingAdminBasePath + "/for-receiving-form/:id",
      parse: (id: string) => outgoingAdminBasePath + `/for-receiving-form/${id}`,
    },
    viewOutgoingAdminTransmittal: {
      key: outgoingAdminBasePath + "/new-forms/outgoing/view/:id",
      parse: (id: string) => outgoingAdminBasePath + `/new-forms/outgoing/view/${id}`,
    },
    viewOutgoingAdminTransmittalTrail: {
      key: outgoingAdminBasePath + "/new-forms/outgoing/transmittaltrail/view/:id",
      parse: (id: string) => outgoingAdminBasePath + `/new-forms/outgoing/transmittaltrail/view/${id}`,
    },
    profile: {
      key: outgoingAdminBasePath + "/profile",
    },
  },
  INCOMINGADMIN: {
    notification: { key: incomingAdminBasePath + "/notification" },
    incomingAdminDashboard: {
      key: incomingAdminBasePath + "/dashboard",
    },
    incomingAdminNewForms: {
      key: incomingAdminBasePath + "/new-forms",
    },
    incomingAdminNewFormsIncoming: {
      key: incomingAdminBasePath + "/new-forms/incoming",
    },
    forIncomingAdminReceivingForm: {
      key: incomingAdminBasePath + "/for-receiving-form/:id",
      parse: (id: string) => incomingAdminBasePath + `/for-receiving-form/${id}`,
    },
    viewIncomingAdminTransmittal: {
      key: incomingAdminBasePath + "/new-forms/incoming/view/:id",
      parse: (id: string) => incomingAdminBasePath + `/new-forms/incoming/view/${id}`,
    },
    viewIncomingAdminTransmittalTrail: {
      key: incomingAdminBasePath + "/new-forms/incoming/transmittaltrail/view/:id",
      parse: (id: string) => incomingAdminBasePath + `/new-forms/incoming/transmittaltrail/view/${id}`,
    },
    profile: {
      key: incomingAdminBasePath + "/profile",
    },
  },
  CHIEFCASHIER: {
    notification: { key: chiefCashierBasePath + "/notification" },
    chiefCashierDashboard: {
      key: chiefCashierBasePath + "/dashboard",
    },
    viewNewTransmittalForm: {
      key: chiefCashierBasePath + "/new-forms/outgoing/view",
    },
    viewNewTransmittal: {
      key: chiefCashierBasePath + "/new-forms/outgoing/view/id",
    },
    inventory: {
      key: chiefCashierBasePath + "/inventory",
    },
    inventoryVerifiedList: {
      key: chiefCashierBasePath + "/inventory/verified-list/:id",
    },
    inventoryNewForms: {
      key: chiefCashierBasePath + "/inventory/new-forms",
    },
    inventoryUsedForms: {
      key: chiefCashierBasePath + "/inventory/used-forms",
    },
    verificationList: {
      key: chiefCashierBasePath + "/verification-list",
    },
    verificationListForm: {
      key: chiefCashierBasePath + "/verification-list/form/:id",
      parse: (id: string) => chiefCashierBasePath + `/verification-list/form/${id}`,
    },
    statusTracking: {
      key: chiefCashierBasePath + "/status-tracking",
    },
    statusTrackingDetails: {
      key: chiefCashierBasePath + "/status-tracking-details/:id",
      parse: (id: string) => chiefCashierBasePath + `/status-tracking-details/${id}`,
    },
    seriesDetails: {
      key: chiefCashierBasePath + "/series-details/:id",
      parse: (id: string) => chiefCashierBasePath + `/series-details/${id}`,
    },
    viewSeriesDetails: {
      key: chiefCashierBasePath + "/view-series-details/:id",
      parse: (id: string) => chiefCashierBasePath + `/view-series-details/${id}`,
    },
    statusTrackingView: {
      key: chiefCashierBasePath + "/status-tracking/view",
    },
    seriesAssignment: {
      key: chiefCashierBasePath + "/series-assignment",
    },
    seriesAssignmentForm: {
      key: chiefCashierBasePath + "/series-assignment/form/:id",
      parse: (id: string) => chiefCashierBasePath + `/series-assignment/form/${id}`,
    },
    viewSeriesAssignment: {
      key: chiefCashierBasePath + "/series-assignment/view/:id",
      parse: (id: string) => chiefCashierBasePath + `/series-assignment/view/${id}`,
    },
    usedForms: {
      key: chiefCashierBasePath + "/used-forms",
    },
    forReturnedReceivingForm: {
      key: chiefCashierBasePath + "/receive-returned-form/:id",
      parse: (id: string) => chiefCashierBasePath + `/receive-returned-form/${id}`,
    },
    viewReturnedForm: {
      key: chiefCashierBasePath + "/view-returned-form/:id",
      parse: (id: string) => chiefCashierBasePath + `/view-returned-form/${id}`,
    },
    requestPads: {
      key: chiefCashierBasePath + "/request-pads",
    },
    viewRequestPadForm: {
      key: chiefCashierBasePath + "/request-pads/view/:id",
      parse: (id: string) => chiefCashierBasePath + `/request-pads/view/${id}`,
    },
    viewForApprovalForm: {
      key: chiefCashierBasePath + "/for-approval-pads/view/:id",
      parse: (id: string) => chiefCashierBasePath + `/for-approval-pads/view/${id}`,
    },
    profile: {
      key: chiefCashierBasePath + "/profile",
    },
  },
  CLIFSAADMIN: {
    notification: { key: clifsaAdminBasePath + "/notification" },
    clifsaAdminDashboard: {
      key: clifsaAdminBasePath + "/dashboard",
    },
    clifsaAdminNewForm: {
      key: clifsaAdminBasePath + "/new-forms",
    },
    clifsaAdminForReceivingForms: {
      key: clifsaAdminBasePath + "/for-receiving-forms",
    },
    viewForReceivingForm: {
      key: clifsaAdminBasePath + "/for-receiving-forms/view/:id",
      parse: (id: string) => clifsaAdminBasePath + `/for-receiving-forms/view/${id}`,
    },
    viewTransmittalForm: {
      key: clifsaAdminBasePath + "/new-forms/view/:id",
      parse: (id: string) => clifsaAdminBasePath + `/new-forms/view/${id}`,
    },
    viewReleasedForm: {
      key: clifsaAdminBasePath + "/released-form/:id",
      parse: (id: string) => clifsaAdminBasePath + `/released-form/${id}`,
    },
    usedForms: {
      key: clifsaAdminBasePath + "/used-forms",
    },
    viewReturnedReceivingForm: {
      key: clifsaAdminBasePath + "/for-return-forms/view/:id",
      parse: (id: string) => clifsaAdminBasePath + `/for-return-forms/view/${id}`,
    },
    viewReturnTransmittalForm: {
      key: clifsaAdminBasePath + "/used-forms/view/:id",
      parse: (id: string) => clifsaAdminBasePath + `/used-forms/view/${id}`,
    },
    viewReturnedForm: {
      key: clifsaAdminBasePath + "/returned-forms/:id",
      parse: (id: string) => clifsaAdminBasePath + `/returned-forms/${id}`,
    },
    requestPads: {
      key: clifsaAdminBasePath + "/requests-pads",
    },
    requestPadForm: {
      key: clifsaAdminBasePath + "/request-pads/view/:id",
      parse: (id: string) => clifsaAdminBasePath + `/request-pads/view/${id}`,
    },
    profile: {
      key: clifsaAdminBasePath + "/profile",
    },
  },
  AREAADMIN: {
    notification: { key: areaAdminBasePath + "/notification" },
    areaAdminDashboard: {
      key: areaAdminBasePath + "/dashboard",
    },
    areaAdminNewForm: {
      key: areaAdminBasePath + "/new-forms",
    },
    areaAdminForReceivingForms: {
      key: areaAdminBasePath + "/for-receiving-forms",
    },
    viewForReceivingForm: {
      key: areaAdminBasePath + "/for-receiving-forms/view/:id",
      parse: (id: string) => areaAdminBasePath + `/for-receiving-forms/view/${id}`,
    },
    viewTransmittalForm: {
      key: areaAdminBasePath + "/new-forms/view/:id",
      parse: (id: string) => areaAdminBasePath + `/new-forms/view/${id}`,
    },
    viewReleasedForm: {
      key: areaAdminBasePath + "/released-form/:id",
      parse: (id: string) => areaAdminBasePath + `/released-form/${id}`,
    },
    usedForms: {
      key: areaAdminBasePath + "/used-forms",
    },
    viewReturnedReceivingForm: {
      key: areaAdminBasePath + "/for-return-forms/view/:id",
      parse: (id: string) => areaAdminBasePath + `/for-return-forms/view/${id}`,
    },
    viewReturnTransmittalForm: {
      key: areaAdminBasePath + "/used-forms/view/:id",
      parse: (id: string) => areaAdminBasePath + `/used-forms/view/${id}`,
    },
    viewReturnedForm: {
      key: areaAdminBasePath + "/returned-forms/:id",
      parse: (id: string) => areaAdminBasePath + `/returned-forms/${id}`,
    },
    requestPads: {
      key: areaAdminBasePath + "/requests-pads",
    },
    requestPadForm: {
      key: areaAdminBasePath + "/request-pads/view/:id",
      parse: (id: string) => areaAdminBasePath + `/request-pads/view/${id}`,
    },
    profile: {
      key: areaAdminBasePath + "/profile",
    },
  },
  GAM: {
    notification: { key: gamBasePath + "/notification" },
    gamDashboard: {
      key: gamBasePath + "/dashboard",
    },
    gamForReceivingForms: {
      key: gamBasePath + "/for-receiving-forms",
    },
    gamAdminNewForm: {
      key: gamBasePath + "/new-forms",
    },
    gamInventory: {
      key: gamBasePath + "/inventory",
    },
    viewForReceivingForm: {
      key: gamBasePath + "/for-receiving-forms/view/:id",
      parse: (id: string) => gamBasePath + `/for-receiving-forms/view/${id}`,
    },
    viewFormReceiving: {
      key: gamBasePath + "/new-forms/view/:id",
      parse: (id: string) => gamBasePath + `/new-forms/view/${id}`,
    },
    viewReturnedForm: {
      key: gamBasePath + "/returned-form/:id",
      parse: (id: string) => gamBasePath + `/returned-form/${id}`,
    },
    requestPads: {
      key: gamBasePath + "/request-pads",
    },
    transmittalReturnedForm: {
      key: gamBasePath + "/return-transmittal",
    },
    viewPrTable: {
      key: gamBasePath + "/pr-table/:id",
      parse: (id: string) => gamBasePath + `/pr-table/${id}`,
    },
    issuePRForm: {
      key: gamBasePath + "/issue-pr-form/:id",
      parse: (id: string) => gamBasePath + `/issue-pr-form/${id}`,
    },
    viewPR: {
      key: gamBasePath + "/view-pr/:id",
      parse: (id: string) => gamBasePath + `/view-pr/${id}`,
    },
    profile: {
      key: gamBasePath + "/profile",
    },
  },
  ADMINSATELLITE: {
    notification: { key: adminSatelliteBasePath + "/notification" },
    adminSatelliteDashboard: {
      key: adminSatelliteBasePath + "/dashboard",
    },
    adminSatelliteForReceivingForms: {
      key: adminSatelliteBasePath + "/for-receiving-forms",
    },
    adminSatelliteAdminNewForm: {
      key: adminSatelliteBasePath + "/new-forms",
    },
    adminSatelliteInventory: {
      key: adminSatelliteBasePath + "/inventory",
    },
    viewForReceivingForm: {
      key: adminSatelliteBasePath + "/for-receiving-forms/view/:id",
      parse: (id: string) => adminSatelliteBasePath + `/for-receiving-forms/view/${id}`,
    },
    viewFormReceiving: {
      key: adminSatelliteBasePath + "/new-forms/view/:id",
      parse: (id: string) => adminSatelliteBasePath + `/new-forms/view/${id}`,
    },
    viewReturnedForm: {
      key: adminSatelliteBasePath + "/returned-form/:id",
      parse: (id: string) => adminSatelliteBasePath + `/returned-form/${id}`,
    },
    requestPads: {
      key: adminSatelliteBasePath + "/request-pads",
    },
    transmittalReturnedForm: {
      key: adminSatelliteBasePath + "/return-transmittal",
    },
    viewPrTable: {
      key: adminSatelliteBasePath + "/pr-table/:id",
      parse: (id: string) => adminSatelliteBasePath + `/pr-table/${id}`,
    },
    issuePRForm: {
      key: adminSatelliteBasePath + "/issue-pr-form/:id",
      parse: (id: string) => adminSatelliteBasePath + `/issue-pr-form/${id}`,
    },
    viewPR: {
      key: adminSatelliteBasePath + "/view-pr/:id",
      parse: (id: string) => adminSatelliteBasePath + `/view-pr/${id}`,
    },
    profile: {
      key: adminSatelliteBasePath + "/profile",
    },
  },
  INCOMINGOUTGOINGCASHIER: {
    notification: { key: incomingOutgoingCashierBasePath + "/notification" },
    incomingOutgoingCashierDashboard: {
      key: incomingOutgoingCashierBasePath + "/dashboard",
    },
    newForms: {
      key: incomingOutgoingCashierBasePath + "/new-forms",
    },
    newFormsIncoming: {
      key: incomingOutgoingCashierBasePath + "/new-forms/incoming",
    },
    viewNewForm: {
      key: incomingOutgoingCashierBasePath + "/new-forms/incoming/view/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/new-forms/incoming/view/${id}`,
    },
    newFormsOutgoing: {
      key: incomingOutgoingCashierBasePath + "/new-forms/outgoing",
    },
    viewOutgoingForm: {
      key: incomingOutgoingCashierBasePath + "/new-forms/outgoing/view/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/new-forms/outgoing/view/${id}`,
    },
    forReceivingForm: {
      key: incomingOutgoingCashierBasePath + "/for-receiving-form/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/for-receiving-form/${id}`,
    },
    releasedForms: {
      key: incomingOutgoingCashierBasePath + "/released-forms/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/released-forms/${id}`,
    },
    usedForms: {
      key: incomingOutgoingCashierBasePath + "/used-forms",
    },
    forReturnedReceivingForm: {
      key: incomingOutgoingCashierBasePath + "/receive-returned-form/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/receive-returned-form/${id}`,
    },
    viewIOCTransmittal: {
      key: incomingOutgoingCashierBasePath + "/used-forms/return/view/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/used-forms/return/view/${id}`,
    },
    viewReturnedForm: {
      key: incomingOutgoingCashierBasePath + "/view-returned-form/:id",
      parse: (id: string) => incomingOutgoingCashierBasePath + `/view-returned-form/${id}`,
    },
    inventory: {
      key: incomingOutgoingCashierBasePath + "/inventory",
    },
    verifiedFormsInventory: {
      key: incomingOutgoingCashierBasePath + "/inventory/verified-forms",
    },
    newFormsInventory: {
      key: incomingOutgoingCashierBasePath + "/inventory/new-forms",
    },
    usedFormInventory: {
      key: incomingOutgoingCashierBasePath + "/inventory/used-forms",
    },
    profile: {
      key: incomingOutgoingCashierBasePath + "/profile",
    },
  },
  UNDERWRITING: {
    notification: { key: underwritingBasePath + "/notification" },
    underwritingDashboard: {
      key: underwritingBasePath + "/dashboard",
    },
    UnderwritingProductProposalApproval: {
      key: underwritingBasePath + "/product-proposal-approvals/underwriting",
    },
    viewUnderwritingProductProposalApproval: {
      key: underwritingBasePath + "/product-proposals-approvals/proposal/underwriting/:id",
      parse: (id: string) => underwritingBasePath + `/product-proposals-approvals/proposal/underwriting/${id}`,
    },
    masterPolicy: {
      key: underwritingBasePath + "/master-policy",
    },
    issuance: {
      key: underwritingBasePath + "/master-policy/issuance",
    },
    issuanceViewProposal: {
      key: underwritingBasePath + "/master-policy/issuance/view-proposal/:id",
      parse: (id: string) => underwritingBasePath + `/master-policy/issuance/view-proposal/${id}`,
    },
    issuanceViewPolicy: {
      key: underwritingBasePath + "/master-policy/issuance/view-policy/:id",
      parse: (id: string) => underwritingBasePath + `/master-policy/issuance/view-policy/${id}`,
    },
    issuedPolicies: {
      key: underwritingBasePath + "/master-policy/issued-policies",
    },
    profile: {
      key: underwritingBasePath + "/profile",
    },
    requestDashboard: {
      key: underwritingBasePath + "/request-dashboard",
    },
    requestForm: {
      key: underwritingBasePath + "/request-form",
    },
    viewRequestForm: {
      key: underwritingBasePath + "/request-form/:id",
      parse: (id: string) => underwritingBasePath + `/request-form/${id}`,
    },
  },
  CLAIMS: {
    notification: { key: claimsBasePath + "/notification" },
    claimsDashboard: {
      key: claimsBasePath + "/dashboard",
    },
    claimsProductProposalApproval: {
      key: claimsBasePath + "/product-proposal-approvals/claims",
    },
    viewClaimsProductProposalApproval: {
      key: claimsBasePath + "/product-proposals-approvals/proposal/claims/:id",
      parse: (id: string) => claimsBasePath + `/product-proposals-approvals/proposal/claims/${id}`,
    },
    requestDashboard: {
      key: claimsBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsBasePath + "/request-form/:id",
      parse: (id: string) => claimsBasePath + `/request-form/${id}`,
    },
    profile: {
      key: claimsBasePath + "/profile",
    },
  },
  ACTUARYANALYST1: {
    notification: { key: actuaryAnalyst1BasePath + "/notification" },
    dashboard: {
      key: actuaryAnalyst1BasePath + "/dashboard",
    },

    quotationFipAerView: {
      key: actuaryAnalyst1BasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => actuaryAnalyst1BasePath + "/quotations/aer-view/fip/" + `${id}`,
    },

    quotationClspQuotationView: {
      key: actuaryAnalyst1BasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => actuaryAnalyst1BasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: actuaryAnalyst1BasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => actuaryAnalyst1BasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: actuaryAnalyst1BasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => actuaryAnalyst1BasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },
    myApprovals: {
      key: actuaryAnalyst1BasePath + "/my-approvals",
    },
    aerApproval: {
      key: actuaryAnalyst1BasePath + "/my-approvals/aer-approvals",
    },
    profile: {
      key: actuaryAnalyst1BasePath + "/profile",
    },
  },
  ACTUARYASSISTANT1: {
    notification: { key: actuaryAssistant1BasePath + "/notification" },
    dashboard: {
      key: actuaryAssistant1BasePath + "/dashboard",
    },
    quotationFipAerView: {
      key: actuaryAssistant1BasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => actuaryAssistant1BasePath + "/quotations/aer-view/fip/" + `${id}`,
    },

    quotationClspQuotationView: {
      key: actuaryAssistant1BasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => actuaryAssistant1BasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: actuaryAssistant1BasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => actuaryAssistant1BasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: actuaryAssistant1BasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => actuaryAssistant1BasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },
    myApprovals: {
      key: actuaryAssistant1BasePath + "/my-approvals",
    },
    aerApproval: {
      key: actuaryAssistant1BasePath + "/my-approvals/aer-approvals",
    },
    profile: {
      key: actuaryAssistant1BasePath + "/profile",
    },
  },

  ACTUARYMANAGER: {
    notification: { key: actuaryManagerBasePath + "/notification" },
    dashboard: {
      key: actuaryManagerBasePath + "/dashboard",
    },
    quotationFipAerView: {
      key: actuaryManagerBasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => actuaryManagerBasePath + "/quotations/aer-view/fip/" + `${id}`,
    },

    quotationClspQuotationView: {
      key: actuaryManagerBasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => actuaryManagerBasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: actuaryManagerBasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => actuaryManagerBasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: actuaryManagerBasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => actuaryManagerBasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },
    myApprovals: {
      key: actuaryManagerBasePath + "/my-approvals",
    },
    aerApproval: {
      key: actuaryManagerBasePath + "/my-approvals/aer-approvals",
    },
    requestDashboard: {
      key: actuaryManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: actuaryManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: actuaryManagerBasePath + "/request-form/:id",
      parse: (id: string) => actuaryManagerBasePath + `/request-form/${id}`,
    },
    profile: {
      key: actuaryManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: actuaryManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: actuaryManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: actuaryManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: actuaryManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: actuaryManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  AVPFORLIFEANDNONLIFE: {
    notification: { key: avpLifeNonLifeManagerBasePath + "/notification" },
    dashboard: {
      key: avpLifeNonLifeManagerBasePath + "/dashboard",
    },

    quotationFipAerView: {
      key: avpLifeNonLifeManagerBasePath + "/quotations/aer-view/fip/:id",
      parse: (id: string) => avpLifeNonLifeManagerBasePath + "/quotations/aer-view/fip/" + `${id}`,
    },

    quotationClspQuotationView: {
      key: avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/clsp/:id",
      parse: (id: string) => avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/clsp/" + `${id}`,
    },
    quotationClppQuotationView: {
      key: avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/clpp/:id",
      parse: (id: string) => avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/clpp/" + `${id}`,
    },
    quotationGyrtQuotationView: {
      key: avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/gyrt/:id",
      parse: (id: string) => avpLifeNonLifeManagerBasePath + "/quotations/quotation-view/gyrt/" + `${id}`,
    },
    myApprovals: {
      key: avpLifeNonLifeManagerBasePath + "/my-approvals",
    },
    aerApproval: {
      key: avpLifeNonLifeManagerBasePath + "/my-approvals/aer-approvals",
    },
    requestDashboard: {
      key: avpLifeNonLifeManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: avpLifeNonLifeManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: avpLifeNonLifeManagerBasePath + "/request-form/:id",
      parse: (id: string) => avpLifeNonLifeManagerBasePath + `/request-form/${id}`,
    },
    profile: {
      key: avpLifeNonLifeManagerBasePath + "/profile",
    },
  },
  RESEARCHANDDEVELOPMENT: {
    dashboard: {
      key: researchAndDevelopmentBasePath + "/dashboard",
    },
    products: {
      key: researchAndDevelopmentBasePath + "/products",
    },
    revisions: {
      key: researchAndDevelopmentBasePath + "/products/:productid/revisions",
      parse: (pid: string) => researchAndDevelopmentBasePath + `/products/${pid}/revisions`,
    },
    reviewRevisions: {
      key: researchAndDevelopmentBasePath + "/products/:productid/revision/:revisionid",
      parse: (pid: string, rid: string) => researchAndDevelopmentBasePath + `/products/${pid}/revision/${rid}`,
    },
    editProductDetails: {
      key: researchAndDevelopmentBasePath + "/product/details/:id",
      parse: (id: string) => researchAndDevelopmentBasePath + `/product/details/${id}`,
    },
    guidelines: {
      key: researchAndDevelopmentBasePath + "/products/guidelines",
    },
    createGuidelines: {
      key: researchAndDevelopmentBasePath + "/products/guidelines/create",
    },
    editGuidelines: {
      key: researchAndDevelopmentBasePath + "/products/guidelines/edit/:id",
      parse: (id: string) => researchAndDevelopmentBasePath + `/products/guidelines/edit/${id}`,
    },
    editRevision: {
      key: researchAndDevelopmentBasePath + "/products/revisions/edit/:id",
      parse: (id: string) => researchAndDevelopmentBasePath + `/products/revisions/edit/${id}`,
    },
    cloneProductGuideline: {
      key: researchAndDevelopmentBasePath + "/products/guidelines/clone",
    },
    cloneProduct: {
      key: researchAndDevelopmentBasePath + "/products/clone",
    },
    reviewProductRevisionsFromEmail: {
      key: researchAndDevelopmentBasePath + "/products/:productid/revision/:revisionid/review",
      parse: (pid: string, rid: string) => researchAndDevelopmentBasePath + `/products/${pid}/revision/${rid}/review`,
    },
    notification: { key: researchAndDevelopmentBasePath + "/notification" },
    profile: {
      key: researchAndDevelopmentBasePath + "/profile",
    },
    productProposal: {
      key: researchAndDevelopmentBasePath + "/product-proposal",
    },
    viewProductProposalCustomize: {
      key: researchAndDevelopmentBasePath + "/product-proposal/:id",
      parse: (id: string) => researchAndDevelopmentBasePath + `/product-proposal/${id}`,
    },
    viewProductProposal: {
      key: researchAndDevelopmentBasePath + "/product-proposal/:id",
      parse: (id: string) => researchAndDevelopmentBasePath + `/product-proposal/${id}`,
    },
  },
  CLAIMSMANAGER: {
    dashboard: {
      key: claimsManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsManagerBasePath + "/request-form/:id",
      parse: (id: string) => claimsManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsManagerBasePath + "/notification" },
    profile: {
      key: claimsManagerBasePath + "/profile",
    },
  },

  VICEPRESIDENTFORSALES: {
    dashboard: {
      key: vicePresidentSalesBasePath + "/dashboard",
    },
    myApprovals: {
      key: vicePresidentSalesBasePath + "/my-approvals",
    },
    commissionAndRequirements: {
      key: vicePresidentSalesBasePath + "/my-approvals/validation/commission-and-requirements",
    },
    viewProductProposalSignatory: {
      key: vicePresidentSalesBasePath + "/approvals/validation/commission-and-requirements/view/:id",
      parse: (id: string) => vicePresidentSalesBasePath + `/approvals/validation/commission-and-requirements/view/${id}`,
    },
    notification: { key: vicePresidentSalesBasePath + "/notification" },
    profile: {
      key: vicePresidentSalesBasePath + "/profile",
    },
  },
  VICEPRESIDENTFOROPERATION: {
    dashboard: {
      key: vicePresidentOperationsBasePath + "/dashboard",
    },
    myApprovals: {
      key: vicePresidentOperationsBasePath + "/my-approvals",
    },
    commissionAndRequirements: {
      key: vicePresidentOperationsBasePath + "/my-approvals/validation/commission-and-requirements",
    },
    viewProductProposalSignatory: {
      key: vicePresidentOperationsBasePath + "/approvals/validation/commission-and-requirements/view/:id",
      parse: (id: string) => vicePresidentOperationsBasePath + `/approvals/validation/commission-and-requirements/view/${id}`,
    },
    notification: { key: vicePresidentOperationsBasePath + "/notification" },
    profile: {
      key: vicePresidentOperationsBasePath + "/profile",
    },
    requestDashboard: {
      key: vicePresidentOperationsBasePath + "/request-dashboard",
    },
    requestForm: {
      key: vicePresidentOperationsBasePath + "/request-form",
    },
    viewRequestForm: {
      key: vicePresidentOperationsBasePath + "/request-form/:id",
      parse: (id: string) => vicePresidentOperationsBasePath + `/request-form/${id}`,
    },
  },
  PRESIDENTANDCEO: {
    dashboard: {
      key: presidentCeoBasePath + "/dashboard",
    },
    myApprovals: {
      key: presidentCeoBasePath + "/my-approvals",
    },
    commissionAndRequirements: {
      key: presidentCeoBasePath + "/my-approvals/validation/commission-and-requirements",
    },
    viewProductProposalSignatory: {
      key: presidentCeoBasePath + "/approvals/validation/commission-and-requirements/view/:id",
      parse: (id: string) => presidentCeoBasePath + `/approvals/validation/commission-and-requirements/view/${id}`,
    },
    requestDashboard: {
      key: presidentCeoBasePath + "/request-dashboard",
    },
    requestForm: {
      key: presidentCeoBasePath + "/request-form",
    },
    viewRequestForm: {
      key: presidentCeoBasePath + "/request-form/:id",
      parse: (id: string) => presidentCeoBasePath + `/request-form/${id}`,
    },
    notification: { key: presidentCeoBasePath + "/notification" },
    profile: {
      key: presidentCeoBasePath + "/profile",
    },
  },
  AREASALESMANAGER: {
    dashboard: {
      key: areaSalesManagerBasePath + "/dashboard",
    },
    myApprovals: {
      key: areaSalesManagerBasePath + "/my-approvals",
    },
    commissionAndRequirements: {
      key: areaSalesManagerBasePath + "/my-approvals/validation/commission-and-requirements",
    },
    viewProductProposalSignatory: {
      key: areaSalesManagerBasePath + "/approvals/validation/commission-and-requirements/view/:id",
      parse: (id: string) => areaSalesManagerBasePath + `/approvals/validation/commission-and-requirements/view/${id}`,
    },
    notification: { key: areaSalesManagerBasePath + "/notification" },
    profile: {
      key: areaSalesManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: areaSalesManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: areaSalesManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: areaSalesManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: areaSalesManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: areaSalesManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  INFRASTRUCTUREOFFICER: {
    dashboard: {
      key: infraOfficerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: infraOfficerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: infraOfficerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: infraOfficerBasePath + "/request-form/:id",
      parse: (id: string) => infraOfficerBasePath + `/request-form/${id}`,
    },
    notification: { key: infraOfficerBasePath + "/notification" },
    profile: {
      key: infraOfficerBasePath + "/profile",
    },
  },
  ACCOUNTING: {
    dashboard: {
      key: accountingBasePath + "/dashboard",
    },
    requestDashboard: {
      key: accountingBasePath + "/request-dashboard",
    },
    requestForm: {
      key: accountingBasePath + "/request-form",
    },
    viewRequestForm: {
      key: accountingBasePath + "/request-form/:id",
      parse: (id: string) => accountingBasePath + `/request-form/${id}`,
    },
    notification: { key: accountingBasePath + "/notification" },
    profile: {
      key: accountingBasePath + "/profile",
    },
  },
  PROPERTY_CUSTODIAN: {
    dashboard: {
      key: propertyCustodianBasePath + "/dashboard",
    },
    requestDashboard: {
      key: propertyCustodianBasePath + "/request-dashboard",
    },
    requestForm: {
      key: propertyCustodianBasePath + "/request-form",
    },
    viewRequestForm: {
      key: propertyCustodianBasePath + "/request-form/:id",
      parse: (id: string) => propertyCustodianBasePath + `/request-form/${id}`,
    },
    notification: { key: propertyCustodianBasePath + "/notification" },
    profile: {
      key: propertyCustodianBasePath + "/profile",
    },
  },
  REGIONAL_SALES_MANAGER: {
    dashboard: {
      key: regionalSalesManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: regionalSalesManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: regionalSalesManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: regionalSalesManagerBasePath + "/request-form/:id",
      parse: (id: string) => regionalSalesManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: regionalSalesManagerBasePath + "/notification" },
    profile: {
      key: regionalSalesManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: regionalSalesManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: regionalSalesManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: regionalSalesManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: regionalSalesManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: regionalSalesManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  VP_AGENCY_DISTRIBUTION_CHANNEL_MANAGEMENT: {
    dashboard: {
      key: vpAgencyDistributionChannelManagementBasePath + "/dashboard",
    },
    requestDashboard: {
      key: vpAgencyDistributionChannelManagementBasePath + "/request-dashboard",
    },
    requestForm: {
      key: vpAgencyDistributionChannelManagementBasePath + "/request-form",
    },
    viewRequestForm: {
      key: vpAgencyDistributionChannelManagementBasePath + "/request-form/:id",
      parse: (id: string) => vpAgencyDistributionChannelManagementBasePath + `/request-form/${id}`,
    },
    notification: { key: vpAgencyDistributionChannelManagementBasePath + "/notification" },
    profile: {
      key: vpAgencyDistributionChannelManagementBasePath + "/profile",
    },
  },
  DATA_PROCESSING_ASSISTANT: {
    dashboard: {
      key: dataProcessingAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: dataProcessingAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: dataProcessingAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: dataProcessingAssistantBasePath + "/request-form/:id",
      parse: (id: string) => dataProcessingAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: dataProcessingAssistantBasePath + "/notification" },
    profile: {
      key: dataProcessingAssistantBasePath + "/profile",
    },
  },
  DATA_PROCESSING_ASSISTANT_1: {
    dashboard: {
      key: dataProcessingAssistant1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: dataProcessingAssistant1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: dataProcessingAssistant1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: dataProcessingAssistant1BasePath + "/request-form/:id",
      parse: (id: string) => dataProcessingAssistant1BasePath + `/request-form/${id}`,
    },
    notification: { key: dataProcessingAssistant1BasePath + "/notification" },
    profile: {
      key: dataProcessingAssistant1BasePath + "/profile",
    },
  },
  MAINTENANCE: {
    dashboard: {
      key: maintenanceBasePath + "/dashboard",
    },
    requestDashboard: {
      key: maintenanceBasePath + "/request-dashboard",
    },
    requestForm: {
      key: maintenanceBasePath + "/request-form",
    },
    viewRequestForm: {
      key: maintenanceBasePath + "/request-form/:id",
      parse: (id: string) => maintenanceBasePath + `/request-form/${id}`,
    },
    notification: { key: maintenanceBasePath + "/notification" },
    profile: {
      key: maintenanceBasePath + "/profile",
    },
  },
  FRONT_END_PROGRAMMER: {
    dashboard: {
      key: frontEndProgrammerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: frontEndProgrammerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: frontEndProgrammerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: frontEndProgrammerBasePath + "/request-form/:id",
      parse: (id: string) => frontEndProgrammerBasePath + `/request-form/${id}`,
    },
    notification: { key: frontEndProgrammerBasePath + "/notification" },
    profile: {
      key: frontEndProgrammerBasePath + "/profile",
    },
  },
  ADMINISTRATIVE_ASSISTANT: {
    dashboard: {
      key: administrativeAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: administrativeAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: administrativeAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: administrativeAssistantBasePath + "/request-form/:id",
      parse: (id: string) => administrativeAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: administrativeAssistantBasePath + "/notification" },
    profile: {
      key: administrativeAssistantBasePath + "/profile",
    },
  },
  MANAGER_AGENCY_DISTRIBUTION_CHANNEL: {
    dashboard: {
      key: managerAgencyDistributionChannelBasePath + "/dashboard",
    },
    requestDashboard: {
      key: managerAgencyDistributionChannelBasePath + "/request-dashboard",
    },
    requestForm: {
      key: managerAgencyDistributionChannelBasePath + "/request-form",
    },
    viewRequestForm: {
      key: managerAgencyDistributionChannelBasePath + "/request-form/:id",
      parse: (id: string) => managerAgencyDistributionChannelBasePath + `/request-form/${id}`,
    },
    notification: { key: managerAgencyDistributionChannelBasePath + "/notification" },
    profile: {
      key: managerAgencyDistributionChannelBasePath + "/profile",
    },
  },
  AVP_ADMIN_CORPLAN: {
    dashboard: {
      key: avpAdminCorplanBasePath + "/dashboard",
    },
    requestDashboard: {
      key: avpAdminCorplanBasePath + "/request-dashboard",
    },
    requestForm: {
      key: avpAdminCorplanBasePath + "/request-form",
    },
    viewRequestForm: {
      key: avpAdminCorplanBasePath + "/request-form/:id",
      parse: (id: string) => avpAdminCorplanBasePath + `/request-form/${id}`,
    },
    notification: { key: avpAdminCorplanBasePath + "/notification" },
    profile: {
      key: avpAdminCorplanBasePath + "/profile",
    },
  },
  BOOKKEEPER: {
    dashboard: {
      key: bookkeeperBasePath + "/dashboard",
    },
    requestDashboard: {
      key: bookkeeperBasePath + "/request-dashboard",
    },
    requestForm: {
      key: bookkeeperBasePath + "/request-form",
    },
    viewRequestForm: {
      key: bookkeeperBasePath + "/request-form/:id",
      parse: (id: string) => bookkeeperBasePath + `/request-form/${id}`,
    },
    notification: { key: bookkeeperBasePath + "/notification" },
    profile: {
      key: bookkeeperBasePath + "/profile",
    },
  },
  ADMIN_SPECIALIST: {
    dashboard: {
      key: adminSpecialistBasePath + "/dashboard",
    },
    requestDashboard: {
      key: adminSpecialistBasePath + "/request-dashboard",
    },
    requestForm: {
      key: adminSpecialistBasePath + "/request-form",
    },
    viewRequestForm: {
      key: adminSpecialistBasePath + "/request-form/:id",
      parse: (id: string) => adminSpecialistBasePath + `/request-form/${id}`,
    },
    notification: { key: adminSpecialistBasePath + "/notification" },
    profile: {
      key: adminSpecialistBasePath + "/profile",
    },
  },
  CLAIMS_ASSISTANT: {
    dashboard: {
      key: claimsAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsAssistantBasePath + "/request-form/:id",
      parse: (id: string) => claimsAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsAssistantBasePath + "/notification" },
    profile: {
      key: claimsAssistantBasePath + "/profile",
    },
  },
  UI_UX_DESIGNER: {
    dashboard: {
      key: uiUxDesignerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: uiUxDesignerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: uiUxDesignerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: uiUxDesignerBasePath + "/request-form/:id",
      parse: (id: string) => uiUxDesignerBasePath + `/request-form/${id}`,
    },
    notification: { key: uiUxDesignerBasePath + "/notification" },
    profile: {
      key: uiUxDesignerBasePath + "/profile",
    },
  },
  LIFE_CASHIER: {
    dashboard: {
      key: lifeCashierBasePath + "/dashboard",
    },
    requestDashboard: {
      key: lifeCashierBasePath + "/request-dashboard",
    },
    requestForm: {
      key: lifeCashierBasePath + "/request-form",
    },
    viewRequestForm: {
      key: lifeCashierBasePath + "/request-form/:id",
      parse: (id: string) => lifeCashierBasePath + `/request-form/${id}`,
    },
    notification: { key: lifeCashierBasePath + "/notification" },
    profile: {
      key: lifeCashierBasePath + "/profile",
    },
  },
  POLICY_ISSUANCE_ASSISTANT: {
    dashboard: {
      key: policyIssuanceAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: policyIssuanceAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: policyIssuanceAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: policyIssuanceAssistantBasePath + "/request-form/:id",
      parse: (id: string) => policyIssuanceAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: policyIssuanceAssistantBasePath + "/notification" },
    profile: {
      key: policyIssuanceAssistantBasePath + "/profile",
    },
  },
  BACK_END_PROGRAMMER: {
    dashboard: {
      key: backEndProgrammerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: backEndProgrammerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: backEndProgrammerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: backEndProgrammerBasePath + "/request-form/:id",
      parse: (id: string) => backEndProgrammerBasePath + `/request-form/${id}`,
    },
    notification: { key: backEndProgrammerBasePath + "/notification" },
    profile: {
      key: backEndProgrammerBasePath + "/profile",
    },
  },
  NL_PROPERTY_CLAIMS_ASSISTANT_1: {
    dashboard: {
      key: nlPropertyClaimsAssistant1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: nlPropertyClaimsAssistant1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: nlPropertyClaimsAssistant1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: nlPropertyClaimsAssistant1BasePath + "/request-form/:id",
      parse: (id: string) => nlPropertyClaimsAssistant1BasePath + `/request-form/${id}`,
    },
    notification: { key: nlPropertyClaimsAssistant1BasePath + "/notification" },
    profile: {
      key: nlPropertyClaimsAssistant1BasePath + "/profile",
    },
  },
  FILING_CLERK: {
    dashboard: {
      key: filingClerkBasePath + "/dashboard",
    },
    requestDashboard: {
      key: filingClerkBasePath + "/request-dashboard",
    },
    requestForm: {
      key: filingClerkBasePath + "/request-form",
    },
    viewRequestForm: {
      key: filingClerkBasePath + "/request-form/:id",
      parse: (id: string) => filingClerkBasePath + `/request-form/${id}`,
    },
    notification: { key: filingClerkBasePath + "/notification" },
    profile: {
      key: filingClerkBasePath + "/profile",
    },
  },
  BOOKKEEPER_NONLIFE: {
    dashboard: {
      key: bookkeeperNonlifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: bookkeeperNonlifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: bookkeeperNonlifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: bookkeeperNonlifeBasePath + "/request-form/:id",
      parse: (id: string) => bookkeeperNonlifeBasePath + `/request-form/${id}`,
    },
    notification: { key: bookkeeperNonlifeBasePath + "/notification" },
    profile: {
      key: bookkeeperNonlifeBasePath + "/profile",
    },
  },
  UNDERWRITING_STAFF: {
    dashboard: {
      key: underwritingStaffBasePath + "/dashboard",
    },
    requestDashboard: {
      key: underwritingStaffBasePath + "/request-dashboard",
    },
    requestForm: {
      key: underwritingStaffBasePath + "/request-form",
    },
    viewRequestForm: {
      key: underwritingStaffBasePath + "/request-form/:id",
      parse: (id: string) => underwritingStaffBasePath + `/request-form/${id}`,
    },
    notification: { key: underwritingStaffBasePath + "/notification" },
    profile: {
      key: underwritingStaffBasePath + "/profile",
    },
  },
  DATA_ENCODER: {
    dashboard: {
      key: dataEncoderBasePath + "/dashboard",
    },
    requestDashboard: {
      key: dataEncoderBasePath + "/request-dashboard",
    },
    requestForm: {
      key: dataEncoderBasePath + "/request-form",
    },
    viewRequestForm: {
      key: dataEncoderBasePath + "/request-form/:id",
      parse: (id: string) => dataEncoderBasePath + `/request-form/${id}`,
    },
    notification: { key: dataEncoderBasePath + "/notification" },
    profile: {
      key: dataEncoderBasePath + "/profile",
    },
  },
  SALES_DEVELOPMENT_OFFICER_NONLIFE: {
    dashboard: {
      key: salesDevelopmentOfficerNonlifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: salesDevelopmentOfficerNonlifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: salesDevelopmentOfficerNonlifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: salesDevelopmentOfficerNonlifeBasePath + "/request-form/:id",
      parse: (id: string) => salesDevelopmentOfficerNonlifeBasePath + `/request-form/${id}`,
    },
    notification: { key: salesDevelopmentOfficerNonlifeBasePath + "/notification" },
    profile: {
      key: salesDevelopmentOfficerNonlifeBasePath + "/profile",
    },
  },
  BUSINESS_DEVELOPMENT_MANAGER: {
    dashboard: {
      key: businessDevelopmentManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: businessDevelopmentManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: businessDevelopmentManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: businessDevelopmentManagerBasePath + "/request-form/:id",
      parse: (id: string) => businessDevelopmentManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: businessDevelopmentManagerBasePath + "/notification" },
    profile: {
      key: businessDevelopmentManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: businessDevelopmentManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: businessDevelopmentManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: businessDevelopmentManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: businessDevelopmentManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: businessDevelopmentManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  MEMBER_RELATIONS_ASSISTANT_2_LIFE: {
    dashboard: {
      key: memberRelationsAssistant2LifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: memberRelationsAssistant2LifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: memberRelationsAssistant2LifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: memberRelationsAssistant2LifeBasePath + "/request-form/:id",
      parse: (id: string) => memberRelationsAssistant2LifeBasePath + `/request-form/${id}`,
    },
    notification: { key: memberRelationsAssistant2LifeBasePath + "/notification" },
    profile: {
      key: memberRelationsAssistant2LifeBasePath + "/profile",
    },
  },
  ASSISTANT_CASHIER_1: {
    dashboard: {
      key: assistantCashier1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: assistantCashier1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: assistantCashier1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: assistantCashier1BasePath + "/request-form/:id",
      parse: (id: string) => assistantCashier1BasePath + `/request-form/${id}`,
    },
    notification: { key: assistantCashier1BasePath + "/notification" },
    profile: {
      key: assistantCashier1BasePath + "/profile",
    },
  },
  TREASURY_OFFICER: {
    dashboard: {
      key: treasuryOfficerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: treasuryOfficerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: treasuryOfficerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: treasuryOfficerBasePath + "/request-form/:id",
      parse: (id: string) => treasuryOfficerBasePath + `/request-form/${id}`,
    },
    notification: { key: treasuryOfficerBasePath + "/notification" },
    profile: {
      key: treasuryOfficerBasePath + "/profile",
    },
  },
  VP_FINANCE_INVESTMENT_TREASURY_COMPLIANCE: {
    dashboard: {
      key: vpFinanceInvestmentTreasuryComplianceBasePath + "/dashboard",
    },
    requestDashboard: {
      key: vpFinanceInvestmentTreasuryComplianceBasePath + "/request-dashboard",
    },
    requestForm: {
      key: vpFinanceInvestmentTreasuryComplianceBasePath + "/request-form",
    },
    viewRequestForm: {
      key: vpFinanceInvestmentTreasuryComplianceBasePath + "/request-form/:id",
      parse: (id: string) => vpFinanceInvestmentTreasuryComplianceBasePath + `/request-form/${id}`,
    },
    notification: { key: vpFinanceInvestmentTreasuryComplianceBasePath + "/notification" },
    profile: {
      key: vpFinanceInvestmentTreasuryComplianceBasePath + "/profile",
    },
  },
  CLAIMS_ASSISTANT_1_LIFE: {
    dashboard: {
      key: claimsAssistant1LifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsAssistant1LifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsAssistant1LifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsAssistant1LifeBasePath + "/request-form/:id",
      parse: (id: string) => claimsAssistant1LifeBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsAssistant1LifeBasePath + "/notification" },
    profile: {
      key: claimsAssistant1LifeBasePath + "/profile",
    },
  },
  ADMIN_ASSISTANT_VP_FINANCE: {
    dashboard: {
      key: adminAssistantVpFinanceBasePath + "/dashboard",
    },
    requestDashboard: {
      key: adminAssistantVpFinanceBasePath + "/request-dashboard",
    },
    requestForm: {
      key: adminAssistantVpFinanceBasePath + "/request-form",
    },
    viewRequestForm: {
      key: adminAssistantVpFinanceBasePath + "/request-form/:id",
      parse: (id: string) => adminAssistantVpFinanceBasePath + `/request-form/${id}`,
    },
    notification: { key: adminAssistantVpFinanceBasePath + "/notification" },
    profile: {
      key: adminAssistantVpFinanceBasePath + "/profile",
    },
  },
  BUSINESS_ANALYST: {
    dashboard: {
      key: businessAnalystBasePath + "/dashboard",
    },
    requestDashboard: {
      key: businessAnalystBasePath + "/request-dashboard",
    },
    requestForm: {
      key: businessAnalystBasePath + "/request-form",
    },
    viewRequestForm: {
      key: businessAnalystBasePath + "/request-form/:id",
      parse: (id: string) => businessAnalystBasePath + `/request-form/${id}`,
    },
    notification: { key: businessAnalystBasePath + "/notification" },
    profile: {
      key: businessAnalystBasePath + "/profile",
    },
  },
  DATA_PROCESSING_MANAGER: {
    dashboard: {
      key: dataProcessingManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: dataProcessingManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: dataProcessingManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: dataProcessingManagerBasePath + "/request-form/:id",
      parse: (id: string) => dataProcessingManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: dataProcessingManagerBasePath + "/notification" },
    profile: {
      key: dataProcessingManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: dataProcessingManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: dataProcessingManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: dataProcessingManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: dataProcessingManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: dataProcessingManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  OIC_OPERATIONS_MANAGER_NON_LIFE: {
    dashboard: {
      key: oicOperationsManagerNonLifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: oicOperationsManagerNonLifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: oicOperationsManagerNonLifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: oicOperationsManagerNonLifeBasePath + "/request-form/:id",
      parse: (id: string) => oicOperationsManagerNonLifeBasePath + `/request-form/${id}`,
    },
    notification: { key: oicOperationsManagerNonLifeBasePath + "/notification" },
    profile: {
      key: oicOperationsManagerNonLifeBasePath + "/profile",
    },
    ticketUtilities: {
      key: oicOperationsManagerNonLifeBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: oicOperationsManagerNonLifeBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: oicOperationsManagerNonLifeBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: oicOperationsManagerNonLifeBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: oicOperationsManagerNonLifeBasePath + "/ticket-utilities/devices-system",
    },
  },
  OIC_SALES_DEVELOPMENT_SPECIALIST_VISAYAS: {
    dashboard: {
      key: oicSalesDevelopmentSpecialistVisayasBasePath + "/dashboard",
    },
    requestDashboard: {
      key: oicSalesDevelopmentSpecialistVisayasBasePath + "/request-dashboard",
    },
    requestForm: {
      key: oicSalesDevelopmentSpecialistVisayasBasePath + "/request-form",
    },
    viewRequestForm: {
      key: oicSalesDevelopmentSpecialistVisayasBasePath + "/request-form/:id",
      parse: (id: string) => oicSalesDevelopmentSpecialistVisayasBasePath + `/request-form/${id}`,
    },
    notification: { key: oicSalesDevelopmentSpecialistVisayasBasePath + "/notification" },
    profile: {
      key: oicSalesDevelopmentSpecialistVisayasBasePath + "/profile",
    },
  },
  CLAIMS_PROCESSOR: {
    dashboard: {
      key: claimsProcessorBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsProcessorBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsProcessorBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsProcessorBasePath + "/request-form/:id",
      parse: (id: string) => claimsProcessorBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsProcessorBasePath + "/notification" },
    profile: {
      key: claimsProcessorBasePath + "/profile",
    },
  },
  ACTUARY_ANALYST: {
    dashboard: {
      key: actuaryAnalystBasePath + "/dashboard",
    },
    requestDashboard: {
      key: actuaryAnalystBasePath + "/request-dashboard",
    },
    requestForm: {
      key: actuaryAnalystBasePath + "/request-form",
    },
    viewRequestForm: {
      key: actuaryAnalystBasePath + "/request-form/:id",
      parse: (id: string) => actuaryAnalystBasePath + `/request-form/${id}`,
    },
    notification: { key: actuaryAnalystBasePath + "/notification" },
    profile: {
      key: actuaryAnalystBasePath + "/profile",
    },
  },
  TECHNICAL_SUPPORT: {
    dashboard: {
      key: technicalSupportBasePath + "/dashboard",
    },
    requestDashboard: {
      key: technicalSupportBasePath + "/request-dashboard",
    },
    requestForm: {
      key: technicalSupportBasePath + "/request-form",
    },
    viewRequestForm: {
      key: technicalSupportBasePath + "/request-form/:id",
      parse: (id: string) => technicalSupportBasePath + `/request-form/${id}`,
    },
    notification: { key: technicalSupportBasePath + "/notification" },
    profile: {
      key: technicalSupportBasePath + "/profile",
    },
  },
  HR_ASSISTANT: {
    dashboard: {
      key: hrAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: hrAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: hrAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: hrAssistantBasePath + "/request-form/:id",
      parse: (id: string) => hrAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: hrAssistantBasePath + "/notification" },
    profile: {
      key: hrAssistantBasePath + "/profile",
    },
  },
  INTERNAL_AUDITOR: {
    dashboard: {
      key: internalAuditorBasePath + "/dashboard",
    },
    requestDashboard: {
      key: internalAuditorBasePath + "/request-dashboard",
    },
    requestForm: {
      key: internalAuditorBasePath + "/request-form",
    },
    viewRequestForm: {
      key: internalAuditorBasePath + "/request-form/:id",
      parse: (id: string) => internalAuditorBasePath + `/request-form/${id}`,
    },
    notification: { key: internalAuditorBasePath + "/notification" },
    profile: {
      key: internalAuditorBasePath + "/profile",
    },
  },
  CORPORATE_PLANNING_ASSISTANT_1: {
    dashboard: {
      key: corporatePlanningAssistant1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: corporatePlanningAssistant1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: corporatePlanningAssistant1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: corporatePlanningAssistant1BasePath + "/request-form/:id",
      parse: (id: string) => corporatePlanningAssistant1BasePath + `/request-form/${id}`,
    },
    notification: { key: corporatePlanningAssistant1BasePath + "/notification" },
    profile: {
      key: corporatePlanningAssistant1BasePath + "/profile",
    },
  },
  SUBSIDIARY_PROJECT_ACCOUNTANT: {
    dashboard: {
      key: subsidiaryProjectAccountantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: subsidiaryProjectAccountantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: subsidiaryProjectAccountantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: subsidiaryProjectAccountantBasePath + "/request-form/:id",
      parse: (id: string) => subsidiaryProjectAccountantBasePath + `/request-form/${id}`,
    },
    notification: { key: subsidiaryProjectAccountantBasePath + "/notification" },
    profile: {
      key: subsidiaryProjectAccountantBasePath + "/profile",
    },
  },
  CLAIMS_ASSISTANT_2: {
    dashboard: {
      key: claimsAssistant2BasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsAssistant2BasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsAssistant2BasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsAssistant2BasePath + "/request-form/:id",
      parse: (id: string) => claimsAssistant2BasePath + `/request-form/${id}`,
    },
    notification: { key: claimsAssistant2BasePath + "/notification" },
    profile: {
      key: claimsAssistant2BasePath + "/profile",
    },
  },
  DISBURSEMENT_ASSISTANT: {
    dashboard: {
      key: disbursementAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: disbursementAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: disbursementAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: disbursementAssistantBasePath + "/request-form/:id",
      parse: (id: string) => disbursementAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: disbursementAssistantBasePath + "/notification" },
    profile: {
      key: disbursementAssistantBasePath + "/profile",
    },
  },
  COMPLIANCE_MANAGER_LIFE: {
    dashboard: {
      key: complianceManagerLifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: complianceManagerLifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: complianceManagerLifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: complianceManagerLifeBasePath + "/request-form/:id",
      parse: (id: string) => complianceManagerLifeBasePath + `/request-form/${id}`,
    },
    notification: { key: complianceManagerLifeBasePath + "/notification" },
    profile: {
      key: complianceManagerLifeBasePath + "/profile",
    },
    ticketUtilities: {
      key: complianceManagerLifeBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: complianceManagerLifeBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: complianceManagerLifeBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: complianceManagerLifeBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: complianceManagerLifeBasePath + "/ticket-utilities/devices-system",
    },
  },
  CLAIMS_ADMIN_ASST: {
    dashboard: {
      key: claimsAdminAsstBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsAdminAsstBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsAdminAsstBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsAdminAsstBasePath + "/request-form/:id",
      parse: (id: string) => claimsAdminAsstBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsAdminAsstBasePath + "/notification" },
    profile: {
      key: claimsAdminAsstBasePath + "/profile",
    },
  },
  DATA_WAREHOUSING_MANAGER: {
    dashboard: {
      key: dataWarehousingManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: dataWarehousingManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: dataWarehousingManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: dataWarehousingManagerBasePath + "/request-form/:id",
      parse: (id: string) => dataWarehousingManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: dataWarehousingManagerBasePath + "/notification" },
    profile: {
      key: dataWarehousingManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: dataWarehousingManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: dataWarehousingManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: dataWarehousingManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: dataWarehousingManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: dataWarehousingManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  SALES_DEVELOPMENT_ANALYST_NCR_CENTRAL_LUZON: {
    dashboard: {
      key: salesDevelopmentAnalystNcrCentralLuzonBasePath + "/dashboard",
    },
    requestDashboard: {
      key: salesDevelopmentAnalystNcrCentralLuzonBasePath + "/request-dashboard",
    },
    requestForm: {
      key: salesDevelopmentAnalystNcrCentralLuzonBasePath + "/request-form",
    },
    viewRequestForm: {
      key: salesDevelopmentAnalystNcrCentralLuzonBasePath + "/request-form/:id",
      parse: (id: string) => salesDevelopmentAnalystNcrCentralLuzonBasePath + `/request-form/${id}`,
    },
    notification: { key: salesDevelopmentAnalystNcrCentralLuzonBasePath + "/notification" },
    profile: {
      key: salesDevelopmentAnalystNcrCentralLuzonBasePath + "/profile",
    },
  },
  UNDERWRITING_ASSISTANT_1: {
    dashboard: {
      key: underwritingAssistant1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: underwritingAssistant1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: underwritingAssistant1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: underwritingAssistant1BasePath + "/request-form/:id",
      parse: (id: string) => underwritingAssistant1BasePath + `/request-form/${id}`,
    },
    notification: { key: underwritingAssistant1BasePath + "/notification" },
    profile: {
      key: underwritingAssistant1BasePath + "/profile",
    },
  },
  OIC_BOOKKEEPER_TIANO: {
    dashboard: {
      key: oicBookkeeperTianoBasePath + "/dashboard",
    },
    requestDashboard: {
      key: oicBookkeeperTianoBasePath + "/request-dashboard",
    },
    requestForm: {
      key: oicBookkeeperTianoBasePath + "/request-form",
    },
    viewRequestForm: {
      key: oicBookkeeperTianoBasePath + "/request-form/:id",
      parse: (id: string) => oicBookkeeperTianoBasePath + `/request-form/${id}`,
    },
    notification: { key: oicBookkeeperTianoBasePath + "/notification" },
    profile: {
      key: oicBookkeeperTianoBasePath + "/profile",
    },
  },
  ADMIN_ASSISTANT_1_VP_ADMIN_CORPLAN: {
    dashboard: {
      key: adminAssistant1VpAdminCorplanBasePath + "/dashboard",
    },
    requestDashboard: {
      key: adminAssistant1VpAdminCorplanBasePath + "/request-dashboard",
    },
    requestForm: {
      key: adminAssistant1VpAdminCorplanBasePath + "/request-form",
    },
    viewRequestForm: {
      key: adminAssistant1VpAdminCorplanBasePath + "/request-form/:id",
      parse: (id: string) => adminAssistant1VpAdminCorplanBasePath + `/request-form/${id}`,
    },
    notification: { key: adminAssistant1VpAdminCorplanBasePath + "/notification" },
    profile: {
      key: adminAssistant1VpAdminCorplanBasePath + "/profile",
    },
  },
  REINSURANCE_SPECIALIST: {
    dashboard: {
      key: reinsuranceSpecialistBasePath + "/dashboard",
    },
    requestDashboard: {
      key: reinsuranceSpecialistBasePath + "/request-dashboard",
    },
    requestForm: {
      key: reinsuranceSpecialistBasePath + "/request-form",
    },
    viewRequestForm: {
      key: reinsuranceSpecialistBasePath + "/request-form/:id",
      parse: (id: string) => reinsuranceSpecialistBasePath + `/request-form/${id}`,
    },
    notification: { key: reinsuranceSpecialistBasePath + "/notification" },
    profile: {
      key: reinsuranceSpecialistBasePath + "/profile",
    },
  },
  COMMUNICATIONS_ASSISTANT: {
    dashboard: {
      key: communicationsAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: communicationsAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: communicationsAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: communicationsAssistantBasePath + "/request-form/:id",
      parse: (id: string) => communicationsAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: communicationsAssistantBasePath + "/notification" },
    profile: {
      key: communicationsAssistantBasePath + "/profile",
    },
  },
  BANK_RECONCILIATION_ASSISTANT_1_LIFE: {
    dashboard: {
      key: bankReconciliationAssistant1LifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: bankReconciliationAssistant1LifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: bankReconciliationAssistant1LifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: bankReconciliationAssistant1LifeBasePath + "/request-form/:id",
      parse: (id: string) => bankReconciliationAssistant1LifeBasePath + `/request-form/${id}`,
    },
    notification: { key: bankReconciliationAssistant1LifeBasePath + "/notification" },
    profile: {
      key: bankReconciliationAssistant1LifeBasePath + "/profile",
    },
  },
  ADMIN_ASST_OUTGOING: {
    dashboard: {
      key: adminAsstOutgoingBasePath + "/dashboard",
    },
    requestDashboard: {
      key: adminAsstOutgoingBasePath + "/request-dashboard",
    },
    requestForm: {
      key: adminAsstOutgoingBasePath + "/request-form",
    },
    viewRequestForm: {
      key: adminAsstOutgoingBasePath + "/request-form/:id",
      parse: (id: string) => adminAsstOutgoingBasePath + `/request-form/${id}`,
    },
    notification: { key: adminAsstOutgoingBasePath + "/notification" },
    profile: {
      key: adminAsstOutgoingBasePath + "/profile",
    },
  },
  OIC_ASM: {
    dashboard: {
      key: oicAsmBasePath + "/dashboard",
    },
    requestDashboard: {
      key: oicAsmBasePath + "/request-dashboard",
    },
    requestForm: {
      key: oicAsmBasePath + "/request-form",
    },
    viewRequestForm: {
      key: oicAsmBasePath + "/request-form/:id",
      parse: (id: string) => oicAsmBasePath + `/request-form/${id}`,
    },
    notification: { key: oicAsmBasePath + "/notification" },
    profile: {
      key: oicAsmBasePath + "/profile",
    },
  },
  ACCOUNTING_ASSISTANT: {
    dashboard: {
      key: accountingAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: accountingAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: accountingAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: accountingAssistantBasePath + "/request-form/:id",
      parse: (id: string) => accountingAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: accountingAssistantBasePath + "/notification" },
    profile: {
      key: accountingAssistantBasePath + "/profile",
    },
  },
  TECHNICAL_WRITER: {
    dashboard: {
      key: technicalWriterBasePath + "/dashboard",
    },
    requestDashboard: {
      key: technicalWriterBasePath + "/request-dashboard",
    },
    requestForm: {
      key: technicalWriterBasePath + "/request-form",
    },
    viewRequestForm: {
      key: technicalWriterBasePath + "/request-form/:id",
      parse: (id: string) => technicalWriterBasePath + `/request-form/${id}`,
    },
    notification: { key: technicalWriterBasePath + "/notification" },
    profile: {
      key: technicalWriterBasePath + "/profile",
    },
  },
  UNDERWRITING_ASSISTANT: {
    dashboard: {
      key: underwritingAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: underwritingAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: underwritingAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: underwritingAssistantBasePath + "/request-form/:id",
      parse: (id: string) => underwritingAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: underwritingAssistantBasePath + "/notification" },
    profile: {
      key: underwritingAssistantBasePath + "/profile",
    },
  },
  SALES_DEVELOPMENT_ANALYST_SOUTH_LUZON: {
    dashboard: {
      key: salesDevelopmentAnalystSouthLuzonBasePath + "/dashboard",
    },
    requestDashboard: {
      key: salesDevelopmentAnalystSouthLuzonBasePath + "/request-dashboard",
    },
    requestForm: {
      key: salesDevelopmentAnalystSouthLuzonBasePath + "/request-form",
    },
    viewRequestForm: {
      key: salesDevelopmentAnalystSouthLuzonBasePath + "/request-form/:id",
      parse: (id: string) => salesDevelopmentAnalystSouthLuzonBasePath + `/request-form/${id}`,
    },
    notification: { key: salesDevelopmentAnalystSouthLuzonBasePath + "/notification" },
    profile: {
      key: salesDevelopmentAnalystSouthLuzonBasePath + "/profile",
    },
  },
  DRIVER: {
    dashboard: {
      key: driverBasePath + "/dashboard",
    },
    requestDashboard: {
      key: driverBasePath + "/request-dashboard",
    },
    requestForm: {
      key: driverBasePath + "/request-form",
    },
    viewRequestForm: {
      key: driverBasePath + "/request-form/:id",
      parse: (id: string) => driverBasePath + `/request-form/${id}`,
    },
    notification: { key: driverBasePath + "/notification" },
    profile: {
      key: driverBasePath + "/profile",
    },
  },
  ACCOUNTANT: {
    dashboard: {
      key: accountantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: accountantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: accountantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: accountantBasePath + "/request-form/:id",
      parse: (id: string) => accountantBasePath + `/request-form/${id}`,
    },
    notification: { key: accountantBasePath + "/notification" },
    profile: {
      key: accountantBasePath + "/profile",
    },
  },
  VP_SALES_MARKETING: {
    dashboard: {
      key: vpSalesMarketingBasePath + "/dashboard",
    },
    requestDashboard: {
      key: vpSalesMarketingBasePath + "/request-dashboard",
    },
    requestForm: {
      key: vpSalesMarketingBasePath + "/request-form",
    },
    viewRequestForm: {
      key: vpSalesMarketingBasePath + "/request-form/:id",
      parse: (id: string) => vpSalesMarketingBasePath + `/request-form/${id}`,
    },
    notification: { key: vpSalesMarketingBasePath + "/notification" },
    profile: {
      key: vpSalesMarketingBasePath + "/profile",
    },
  },
  SOCIAL_MEDIA_ASSISTANT: {
    dashboard: {
      key: socialMediaAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: socialMediaAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: socialMediaAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: socialMediaAssistantBasePath + "/request-form/:id",
      parse: (id: string) => socialMediaAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: socialMediaAssistantBasePath + "/notification" },
    profile: {
      key: socialMediaAssistantBasePath + "/profile",
    },
  },
  ADMINISTRATIVE_ASSISTANT_OPERATIONS_LIFE: {
    dashboard: {
      key: administrativeAssistantOperationsLifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: administrativeAssistantOperationsLifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: administrativeAssistantOperationsLifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: administrativeAssistantOperationsLifeBasePath + "/request-form/:id",
      parse: (id: string) => administrativeAssistantOperationsLifeBasePath + `/request-form/${id}`,
    },
    notification: { key: administrativeAssistantOperationsLifeBasePath + "/notification" },
    profile: {
      key: administrativeAssistantOperationsLifeBasePath + "/profile",
    },
  },
  VP_ADMIN_CORPLAN_CEO_PRINCIPAL_CCP: {
    dashboard: {
      key: vpAdminCorplanCeoPrincipalCcpBasePath + "/dashboard",
    },
    requestDashboard: {
      key: vpAdminCorplanCeoPrincipalCcpBasePath + "/request-dashboard",
    },
    requestForm: {
      key: vpAdminCorplanCeoPrincipalCcpBasePath + "/request-form",
    },
    viewRequestForm: {
      key: vpAdminCorplanCeoPrincipalCcpBasePath + "/request-form/:id",
      parse: (id: string) => vpAdminCorplanCeoPrincipalCcpBasePath + `/request-form/${id}`,
    },
    notification: { key: vpAdminCorplanCeoPrincipalCcpBasePath + "/notification" },
    profile: {
      key: vpAdminCorplanCeoPrincipalCcpBasePath + "/profile",
    },
  },
  FIRE_MARSHALL: {
    dashboard: {
      key: fireMarshallBasePath + "/dashboard",
    },
    requestDashboard: {
      key: fireMarshallBasePath + "/request-dashboard",
    },
    requestForm: {
      key: fireMarshallBasePath + "/request-form",
    },
    viewRequestForm: {
      key: fireMarshallBasePath + "/request-form/:id",
      parse: (id: string) => fireMarshallBasePath + `/request-form/${id}`,
    },
    notification: { key: fireMarshallBasePath + "/notification" },
    profile: {
      key: fireMarshallBasePath + "/profile",
    },
  },
  MESSENGER_UTILITY_STAFF: {
    dashboard: {
      key: messengerUtilityStaffBasePath + "/dashboard",
    },
    requestDashboard: {
      key: messengerUtilityStaffBasePath + "/request-dashboard",
    },
    requestForm: {
      key: messengerUtilityStaffBasePath + "/request-form",
    },
    viewRequestForm: {
      key: messengerUtilityStaffBasePath + "/request-form/:id",
      parse: (id: string) => messengerUtilityStaffBasePath + `/request-form/${id}`,
    },
    notification: { key: messengerUtilityStaffBasePath + "/notification" },
    profile: {
      key: messengerUtilityStaffBasePath + "/profile",
    },
  },
  BORDEREAUX: {
    dashboard: {
      key: bordereauxBasePath + "/dashboard",
    },
    requestDashboard: {
      key: bordereauxBasePath + "/request-dashboard",
    },
    requestForm: {
      key: bordereauxBasePath + "/request-form",
    },
    viewRequestForm: {
      key: bordereauxBasePath + "/request-form/:id",
      parse: (id: string) => bordereauxBasePath + `/request-form/${id}`,
    },
    notification: { key: bordereauxBasePath + "/notification" },
    profile: {
      key: bordereauxBasePath + "/profile",
    },
  },
  BANK_RECONCILIATION_ASSISTANT_1: {
    dashboard: {
      key: bankReconciliationAssistant1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: bankReconciliationAssistant1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: bankReconciliationAssistant1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: bankReconciliationAssistant1BasePath + "/request-form/:id",
      parse: (id: string) => bankReconciliationAssistant1BasePath + `/request-form/${id}`,
    },
    notification: { key: bankReconciliationAssistant1BasePath + "/notification" },
    profile: {
      key: bankReconciliationAssistant1BasePath + "/profile",
    },
  },
  NAFECOOP_BUSINESS_DEVT_ASSISTANT: {
    dashboard: {
      key: nafecoopBusinessDevtAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: nafecoopBusinessDevtAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: nafecoopBusinessDevtAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: nafecoopBusinessDevtAssistantBasePath + "/request-form/:id",
      parse: (id: string) => nafecoopBusinessDevtAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: nafecoopBusinessDevtAssistantBasePath + "/notification" },
    profile: {
      key: nafecoopBusinessDevtAssistantBasePath + "/profile",
    },
  },
  QUALITY_DATA_PROCESSOR_RETRIEVAL_TECHNICAL_SUPPORT: {
    dashboard: {
      key: qualityDataProcessorRetrievalTechnicalSupportBasePath + "/dashboard",
    },
    requestDashboard: {
      key: qualityDataProcessorRetrievalTechnicalSupportBasePath + "/request-dashboard",
    },
    requestForm: {
      key: qualityDataProcessorRetrievalTechnicalSupportBasePath + "/request-form",
    },
    viewRequestForm: {
      key: qualityDataProcessorRetrievalTechnicalSupportBasePath + "/request-form/:id",
      parse: (id: string) => qualityDataProcessorRetrievalTechnicalSupportBasePath + `/request-form/${id}`,
    },
    notification: { key: qualityDataProcessorRetrievalTechnicalSupportBasePath + "/notification" },
    profile: {
      key: qualityDataProcessorRetrievalTechnicalSupportBasePath + "/profile",
    },
  },
  ACCOUNTING_ASSISTANT_1: {
    dashboard: {
      key: accountingAssistant1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: accountingAssistant1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: accountingAssistant1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: accountingAssistant1BasePath + "/request-form/:id",
      parse: (id: string) => accountingAssistant1BasePath + `/request-form/${id}`,
    },
    notification: { key: accountingAssistant1BasePath + "/notification" },
    profile: {
      key: accountingAssistant1BasePath + "/profile",
    },
  },
  QA_DOCUMENTATION_ANALYST_1: {
    dashboard: {
      key: qaDocumentationAnalyst1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: qaDocumentationAnalyst1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: qaDocumentationAnalyst1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: qaDocumentationAnalyst1BasePath + "/request-form/:id",
      parse: (id: string) => qaDocumentationAnalyst1BasePath + `/request-form/${id}`,
    },
    notification: { key: qaDocumentationAnalyst1BasePath + "/notification" },
    profile: {
      key: qaDocumentationAnalyst1BasePath + "/profile",
    },
  },
  ACTUARY_ANALYST_2: {
    dashboard: {
      key: actuaryAnalyst2BasePath + "/dashboard",
    },
    requestDashboard: {
      key: actuaryAnalyst2BasePath + "/request-dashboard",
    },
    requestForm: {
      key: actuaryAnalyst2BasePath + "/request-form",
    },
    viewRequestForm: {
      key: actuaryAnalyst2BasePath + "/request-form/:id",
      parse: (id: string) => actuaryAnalyst2BasePath + `/request-form/${id}`,
    },
    notification: { key: actuaryAnalyst2BasePath + "/notification" },
    profile: {
      key: actuaryAnalyst2BasePath + "/profile",
    },
  },
  AVP_INVESTMENT_TREASURY: {
    dashboard: {
      key: avpInvestmentTreasuryBasePath + "/dashboard",
    },
    requestDashboard: {
      key: avpInvestmentTreasuryBasePath + "/request-dashboard",
    },
    requestForm: {
      key: avpInvestmentTreasuryBasePath + "/request-form",
    },
    viewRequestForm: {
      key: avpInvestmentTreasuryBasePath + "/request-form/:id",
      parse: (id: string) => avpInvestmentTreasuryBasePath + `/request-form/${id}`,
    },
    notification: { key: avpInvestmentTreasuryBasePath + "/notification" },
    profile: {
      key: avpInvestmentTreasuryBasePath + "/profile",
    },
  },
  COLLECTION_ANALYST: {
    dashboard: {
      key: collectionAnalystBasePath + "/dashboard",
    },
    requestDashboard: {
      key: collectionAnalystBasePath + "/request-dashboard",
    },
    requestForm: {
      key: collectionAnalystBasePath + "/request-form",
    },
    viewRequestForm: {
      key: collectionAnalystBasePath + "/request-form/:id",
      parse: (id: string) => collectionAnalystBasePath + `/request-form/${id}`,
    },
    notification: { key: collectionAnalystBasePath + "/notification" },
    profile: {
      key: collectionAnalystBasePath + "/profile",
    },
  },
  DIGITAL_MEDIA_ASSISTANT_RELIEVER: {
    dashboard: {
      key: digitalMediaAssistantRelieverBasePath + "/dashboard",
    },
    requestDashboard: {
      key: digitalMediaAssistantRelieverBasePath + "/request-dashboard",
    },
    requestForm: {
      key: digitalMediaAssistantRelieverBasePath + "/request-form",
    },
    viewRequestForm: {
      key: digitalMediaAssistantRelieverBasePath + "/request-form/:id",
      parse: (id: string) => digitalMediaAssistantRelieverBasePath + `/request-form/${id}`,
    },
    notification: { key: digitalMediaAssistantRelieverBasePath + "/notification" },
    profile: {
      key: digitalMediaAssistantRelieverBasePath + "/profile",
    },
  },
  INVESTMENT_ASSISTANT: {
    dashboard: {
      key: investmentAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: investmentAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: investmentAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: investmentAssistantBasePath + "/request-form/:id",
      parse: (id: string) => investmentAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: investmentAssistantBasePath + "/notification" },
    profile: {
      key: investmentAssistantBasePath + "/profile",
    },
  },
  LEARNING_PROGRAM_COORDINATOR: {
    dashboard: {
      key: learningProgramCoordinatorBasePath + "/dashboard",
    },
    requestDashboard: {
      key: learningProgramCoordinatorBasePath + "/request-dashboard",
    },
    requestForm: {
      key: learningProgramCoordinatorBasePath + "/request-form",
    },
    viewRequestForm: {
      key: learningProgramCoordinatorBasePath + "/request-form/:id",
      parse: (id: string) => learningProgramCoordinatorBasePath + `/request-form/${id}`,
    },
    notification: { key: learningProgramCoordinatorBasePath + "/notification" },
    profile: {
      key: learningProgramCoordinatorBasePath + "/profile",
    },
  },
  QUALITY_ASSURANCE_DOCUMENTATION_ASSISTANT_1: {
    dashboard: {
      key: qualityAssuranceDocumentationAssistant1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: qualityAssuranceDocumentationAssistant1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: qualityAssuranceDocumentationAssistant1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: qualityAssuranceDocumentationAssistant1BasePath + "/request-form/:id",
      parse: (id: string) => qualityAssuranceDocumentationAssistant1BasePath + `/request-form/${id}`,
    },
    notification: { key: qualityAssuranceDocumentationAssistant1BasePath + "/notification" },
    profile: {
      key: qualityAssuranceDocumentationAssistant1BasePath + "/profile",
    },
  },
  BUSINESS_DEVELOPMENT_JUNIOR_MANAGER: {
    dashboard: {
      key: businessDevelopmentJuniorManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: businessDevelopmentJuniorManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: businessDevelopmentJuniorManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: businessDevelopmentJuniorManagerBasePath + "/request-form/:id",
      parse: (id: string) => businessDevelopmentJuniorManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: businessDevelopmentJuniorManagerBasePath + "/notification" },
    profile: {
      key: businessDevelopmentJuniorManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: businessDevelopmentJuniorManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: businessDevelopmentJuniorManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: businessDevelopmentJuniorManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: businessDevelopmentJuniorManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: businessDevelopmentJuniorManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  AVP_NL_SALES_LUZON: {
    dashboard: {
      key: avpNlSalesLuzonBasePath + "/dashboard",
    },
    requestDashboard: {
      key: avpNlSalesLuzonBasePath + "/request-dashboard",
    },
    requestForm: {
      key: avpNlSalesLuzonBasePath + "/request-form",
    },
    viewRequestForm: {
      key: avpNlSalesLuzonBasePath + "/request-form/:id",
      parse: (id: string) => avpNlSalesLuzonBasePath + `/request-form/${id}`,
    },
    notification: { key: avpNlSalesLuzonBasePath + "/notification" },
    profile: {
      key: avpNlSalesLuzonBasePath + "/profile",
    },
  },
  EXECUTIVE_MANAGER: {
    dashboard: {
      key: executiveManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: executiveManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: executiveManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: executiveManagerBasePath + "/request-form/:id",
      parse: (id: string) => executiveManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: executiveManagerBasePath + "/notification" },
    profile: {
      key: executiveManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: executiveManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: executiveManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: executiveManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: executiveManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: executiveManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  UNDERWRITER: {
    dashboard: {
      key: underwriterBasePath + "/dashboard",
    },
    requestDashboard: {
      key: underwriterBasePath + "/request-dashboard",
    },
    requestForm: {
      key: underwriterBasePath + "/request-form",
    },
    viewRequestForm: {
      key: underwriterBasePath + "/request-form/:id",
      parse: (id: string) => underwriterBasePath + `/request-form/${id}`,
    },
    notification: { key: underwriterBasePath + "/notification" },
    profile: {
      key: underwriterBasePath + "/profile",
    },
  },
  ADMIN_ASST_CASHIER: {
    dashboard: {
      key: adminAsstCashierBasePath + "/dashboard",
    },
    requestDashboard: {
      key: adminAsstCashierBasePath + "/request-dashboard",
    },
    requestForm: {
      key: adminAsstCashierBasePath + "/request-form",
    },
    viewRequestForm: {
      key: adminAsstCashierBasePath + "/request-form/:id",
      parse: (id: string) => adminAsstCashierBasePath + `/request-form/${id}`,
    },
    notification: { key: adminAsstCashierBasePath + "/notification" },
    profile: {
      key: adminAsstCashierBasePath + "/profile",
    },
  },
  ADMINISTRATIVE_ASSISTANT_PROCUREMENT: {
    dashboard: {
      key: administrativeAssistantProcurementBasePath + "/dashboard",
    },
    requestDashboard: {
      key: administrativeAssistantProcurementBasePath + "/request-dashboard",
    },
    requestForm: {
      key: administrativeAssistantProcurementBasePath + "/request-form",
    },
    viewRequestForm: {
      key: administrativeAssistantProcurementBasePath + "/request-form/:id",
      parse: (id: string) => administrativeAssistantProcurementBasePath + `/request-form/${id}`,
    },
    notification: { key: administrativeAssistantProcurementBasePath + "/notification" },
    profile: {
      key: administrativeAssistantProcurementBasePath + "/profile",
    },
  },
  POLICY_ISSUANCE_ASST: {
    dashboard: {
      key: policyIssuanceAsstBasePath + "/dashboard",
    },
    requestDashboard: {
      key: policyIssuanceAsstBasePath + "/request-dashboard",
    },
    requestForm: {
      key: policyIssuanceAsstBasePath + "/request-form",
    },
    viewRequestForm: {
      key: policyIssuanceAsstBasePath + "/request-form/:id",
      parse: (id: string) => policyIssuanceAsstBasePath + `/request-form/${id}`,
    },
    notification: { key: policyIssuanceAsstBasePath + "/notification" },
    profile: {
      key: policyIssuanceAsstBasePath + "/profile",
    },
  },
  OIC_UNDERWRITING_OFFICER: {
    dashboard: {
      key: oicUnderwritingOfficerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: oicUnderwritingOfficerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: oicUnderwritingOfficerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: oicUnderwritingOfficerBasePath + "/request-form/:id",
      parse: (id: string) => oicUnderwritingOfficerBasePath + `/request-form/${id}`,
    },
    notification: { key: oicUnderwritingOfficerBasePath + "/notification" },
    profile: {
      key: oicUnderwritingOfficerBasePath + "/profile",
    },
  },
  SYSTEM_PROGRAMMER: {
    dashboard: {
      key: systemProgrammerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: systemProgrammerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: systemProgrammerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: systemProgrammerBasePath + "/request-form/:id",
      parse: (id: string) => systemProgrammerBasePath + `/request-form/${id}`,
    },
    notification: { key: systemProgrammerBasePath + "/notification" },
    profile: {
      key: systemProgrammerBasePath + "/profile",
    },
  },
  TREASURY_ASSISTANT_1: {
    dashboard: {
      key: treasuryAssistant1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: treasuryAssistant1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: treasuryAssistant1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: treasuryAssistant1BasePath + "/request-form/:id",
      parse: (id: string) => treasuryAssistant1BasePath + `/request-form/${id}`,
    },
    notification: { key: treasuryAssistant1BasePath + "/notification" },
    profile: {
      key: treasuryAssistant1BasePath + "/profile",
    },
  },
  BOOKKEEPER_NL: {
    dashboard: {
      key: bookkeeperNlBasePath + "/dashboard",
    },
    requestDashboard: {
      key: bookkeeperNlBasePath + "/request-dashboard",
    },
    requestForm: {
      key: bookkeeperNlBasePath + "/request-form",
    },
    viewRequestForm: {
      key: bookkeeperNlBasePath + "/request-form/:id",
      parse: (id: string) => bookkeeperNlBasePath + `/request-form/${id}`,
    },
    notification: { key: bookkeeperNlBasePath + "/notification" },
    profile: {
      key: bookkeeperNlBasePath + "/profile",
    },
  },
  NETWORK_ADMINISTRATOR: {
    dashboard: {
      key: networkAdministratorBasePath + "/dashboard",
    },
    requestDashboard: {
      key: networkAdministratorBasePath + "/request-dashboard",
    },
    requestForm: {
      key: networkAdministratorBasePath + "/request-form",
    },
    viewRequestForm: {
      key: networkAdministratorBasePath + "/request-form/:id",
      parse: (id: string) => networkAdministratorBasePath + `/request-form/${id}`,
    },
    notification: { key: networkAdministratorBasePath + "/notification" },
    profile: {
      key: networkAdministratorBasePath + "/profile",
    },
  },
  CLAIMS_ASSISTANT_2_MICROINSURANCE: {
    dashboard: {
      key: claimsAssistant2MicroinsuranceBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsAssistant2MicroinsuranceBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsAssistant2MicroinsuranceBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsAssistant2MicroinsuranceBasePath + "/request-form/:id",
      parse: (id: string) => claimsAssistant2MicroinsuranceBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsAssistant2MicroinsuranceBasePath + "/notification" },
    profile: {
      key: claimsAssistant2MicroinsuranceBasePath + "/profile",
    },
  },
  CASHIERING_ASSISTANT: {
    dashboard: {
      key: cashieringAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: cashieringAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: cashieringAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: cashieringAssistantBasePath + "/request-form/:id",
      parse: (id: string) => cashieringAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: cashieringAssistantBasePath + "/notification" },
    profile: {
      key: cashieringAssistantBasePath + "/profile",
    },
  },
  AVP_SALES_LIFE: {
    dashboard: {
      key: avpSalesLifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: avpSalesLifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: avpSalesLifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: avpSalesLifeBasePath + "/request-form/:id",
      parse: (id: string) => avpSalesLifeBasePath + `/request-form/${id}`,
    },
    notification: { key: avpSalesLifeBasePath + "/notification" },
    profile: {
      key: avpSalesLifeBasePath + "/profile",
    },
  },
  CLAIMS_EXAMINER: {
    dashboard: {
      key: claimsExaminerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsExaminerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsExaminerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsExaminerBasePath + "/request-form/:id",
      parse: (id: string) => claimsExaminerBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsExaminerBasePath + "/notification" },
    profile: {
      key: claimsExaminerBasePath + "/profile",
    },
  },
  MARKETING_ASSISTANT: {
    dashboard: {
      key: marketingAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: marketingAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: marketingAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: marketingAssistantBasePath + "/request-form/:id",
      parse: (id: string) => marketingAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: marketingAssistantBasePath + "/notification" },
    profile: {
      key: marketingAssistantBasePath + "/profile",
    },
  },
  OIC_COMPENSATION_BENEFITS_ANALYST_1: {
    dashboard: {
      key: oicCompensationBenefitsAnalyst1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: oicCompensationBenefitsAnalyst1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: oicCompensationBenefitsAnalyst1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: oicCompensationBenefitsAnalyst1BasePath + "/request-form/:id",
      parse: (id: string) => oicCompensationBenefitsAnalyst1BasePath + `/request-form/${id}`,
    },
    notification: { key: oicCompensationBenefitsAnalyst1BasePath + "/notification" },
    profile: {
      key: oicCompensationBenefitsAnalyst1BasePath + "/profile",
    },
  },
  OPERATIONS_MANAGER: {
    dashboard: {
      key: operationsManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: operationsManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: operationsManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: operationsManagerBasePath + "/request-form/:id",
      parse: (id: string) => operationsManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: operationsManagerBasePath + "/notification" },
    profile: {
      key: operationsManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: operationsManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: operationsManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: operationsManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: operationsManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: operationsManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  INSURTECH_SENIOR_MANAGER: {
    dashboard: {
      key: insurtechSeniorManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: insurtechSeniorManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: insurtechSeniorManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: insurtechSeniorManagerBasePath + "/request-form/:id",
      parse: (id: string) => insurtechSeniorManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: insurtechSeniorManagerBasePath + "/notification" },
    profile: {
      key: insurtechSeniorManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: insurtechSeniorManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: insurtechSeniorManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: insurtechSeniorManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: insurtechSeniorManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: insurtechSeniorManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  MEMBER_RELATIONS_ASSISTANT: {
    dashboard: {
      key: memberRelationsAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: memberRelationsAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: memberRelationsAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: memberRelationsAssistantBasePath + "/request-form/:id",
      parse: (id: string) => memberRelationsAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: memberRelationsAssistantBasePath + "/notification" },
    profile: {
      key: memberRelationsAssistantBasePath + "/profile",
    },
  },
  AVP_NONLIFE_SALES_VISMIN: {
    dashboard: {
      key: avpNonlifeSalesVisminBasePath + "/dashboard",
    },
    requestDashboard: {
      key: avpNonlifeSalesVisminBasePath + "/request-dashboard",
    },
    requestForm: {
      key: avpNonlifeSalesVisminBasePath + "/request-form",
    },
    viewRequestForm: {
      key: avpNonlifeSalesVisminBasePath + "/request-form/:id",
      parse: (id: string) => avpNonlifeSalesVisminBasePath + `/request-form/${id}`,
    },
    notification: { key: avpNonlifeSalesVisminBasePath + "/notification" },
    profile: {
      key: avpNonlifeSalesVisminBasePath + "/profile",
    },
  },
  CLAIMS_MANAGER_LIFE: {
    dashboard: {
      key: claimsManagerLifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsManagerLifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsManagerLifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsManagerLifeBasePath + "/request-form/:id",
      parse: (id: string) => claimsManagerLifeBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsManagerLifeBasePath + "/notification" },
    profile: {
      key: claimsManagerLifeBasePath + "/profile",
    },
    ticketUtilities: {
      key: claimsManagerLifeBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: claimsManagerLifeBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: claimsManagerLifeBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: claimsManagerLifeBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: claimsManagerLifeBasePath + "/ticket-utilities/devices-system",
    },
  },
  SALES_DEVELOPMENT_ANALYST_SOUTH_MINDANAO: {
    dashboard: {
      key: salesDevelopmentAnalystSouthMindanaoBasePath + "/dashboard",
    },
    requestDashboard: {
      key: salesDevelopmentAnalystSouthMindanaoBasePath + "/request-dashboard",
    },
    requestForm: {
      key: salesDevelopmentAnalystSouthMindanaoBasePath + "/request-form",
    },
    viewRequestForm: {
      key: salesDevelopmentAnalystSouthMindanaoBasePath + "/request-form/:id",
      parse: (id: string) => salesDevelopmentAnalystSouthMindanaoBasePath + `/request-form/${id}`,
    },
    notification: { key: salesDevelopmentAnalystSouthMindanaoBasePath + "/notification" },
    profile: {
      key: salesDevelopmentAnalystSouthMindanaoBasePath + "/profile",
    },
  },
  NL_REINSURANCE_2: {
    dashboard: {
      key: nlReinsurance2BasePath + "/dashboard",
    },
    requestDashboard: {
      key: nlReinsurance2BasePath + "/request-dashboard",
    },
    requestForm: {
      key: nlReinsurance2BasePath + "/request-form",
    },
    viewRequestForm: {
      key: nlReinsurance2BasePath + "/request-form/:id",
      parse: (id: string) => nlReinsurance2BasePath + `/request-form/${id}`,
    },
    notification: { key: nlReinsurance2BasePath + "/notification" },
    profile: {
      key: nlReinsurance2BasePath + "/profile",
    },
  },
  SYSTEM_DEVELOPMENT_SYSTEM_ADMINISTRATION_MANAGER: {
    dashboard: {
      key: systemDevelopmentSystemAdministrationManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: systemDevelopmentSystemAdministrationManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: systemDevelopmentSystemAdministrationManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: systemDevelopmentSystemAdministrationManagerBasePath + "/request-form/:id",
      parse: (id: string) => systemDevelopmentSystemAdministrationManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: systemDevelopmentSystemAdministrationManagerBasePath + "/notification" },
    profile: {
      key: systemDevelopmentSystemAdministrationManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: systemDevelopmentSystemAdministrationManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: systemDevelopmentSystemAdministrationManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: systemDevelopmentSystemAdministrationManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: systemDevelopmentSystemAdministrationManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: systemDevelopmentSystemAdministrationManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  LEGAL_COUNSEL_IN_HOUSE: {
    dashboard: {
      key: legalCounselInHouseBasePath + "/dashboard",
    },
    requestDashboard: {
      key: legalCounselInHouseBasePath + "/request-dashboard",
    },
    requestForm: {
      key: legalCounselInHouseBasePath + "/request-form",
    },
    viewRequestForm: {
      key: legalCounselInHouseBasePath + "/request-form/:id",
      parse: (id: string) => legalCounselInHouseBasePath + `/request-form/${id}`,
    },
    notification: { key: legalCounselInHouseBasePath + "/notification" },
    profile: {
      key: legalCounselInHouseBasePath + "/profile",
    },
  },
  AVP_LIFE_NONLIFE: {
    dashboard: {
      key: avpLifeNonlifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: avpLifeNonlifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: avpLifeNonlifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: avpLifeNonlifeBasePath + "/request-form/:id",
      parse: (id: string) => avpLifeNonlifeBasePath + `/request-form/${id}`,
    },
    notification: { key: avpLifeNonlifeBasePath + "/notification" },
    profile: {
      key: avpLifeNonlifeBasePath + "/profile",
    },
  },
  JUNIOR_PROGRAMMER_1: {
    dashboard: {
      key: juniorProgrammer1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: juniorProgrammer1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: juniorProgrammer1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: juniorProgrammer1BasePath + "/request-form/:id",
      parse: (id: string) => juniorProgrammer1BasePath + `/request-form/${id}`,
    },
    notification: { key: juniorProgrammer1BasePath + "/notification" },
    profile: {
      key: juniorProgrammer1BasePath + "/profile",
    },
  },
  POLICY_ISSUANCE_NL: {
    dashboard: {
      key: policyIssuanceNlBasePath + "/dashboard",
    },
    requestDashboard: {
      key: policyIssuanceNlBasePath + "/request-dashboard",
    },
    requestForm: {
      key: policyIssuanceNlBasePath + "/request-form",
    },
    viewRequestForm: {
      key: policyIssuanceNlBasePath + "/request-form/:id",
      parse: (id: string) => policyIssuanceNlBasePath + `/request-form/${id}`,
    },
    notification: { key: policyIssuanceNlBasePath + "/notification" },
    profile: {
      key: policyIssuanceNlBasePath + "/profile",
    },
  },
  POLICY_ISSUANCE_CLERK: {
    dashboard: {
      key: policyIssuanceClerkBasePath + "/dashboard",
    },
    requestDashboard: {
      key: policyIssuanceClerkBasePath + "/request-dashboard",
    },
    requestForm: {
      key: policyIssuanceClerkBasePath + "/request-form",
    },
    viewRequestForm: {
      key: policyIssuanceClerkBasePath + "/request-form/:id",
      parse: (id: string) => policyIssuanceClerkBasePath + `/request-form/${id}`,
    },
    notification: { key: policyIssuanceClerkBasePath + "/notification" },
    profile: {
      key: policyIssuanceClerkBasePath + "/profile",
    },
  },
  COMPLIANCE_MANAGER_NONLIFE: {
    dashboard: {
      key: complianceManagerNonlifeBasePath + "/dashboard",
    },
    requestDashboard: {
      key: complianceManagerNonlifeBasePath + "/request-dashboard",
    },
    requestForm: {
      key: complianceManagerNonlifeBasePath + "/request-form",
    },
    viewRequestForm: {
      key: complianceManagerNonlifeBasePath + "/request-form/:id",
      parse: (id: string) => complianceManagerNonlifeBasePath + `/request-form/${id}`,
    },
    notification: { key: complianceManagerNonlifeBasePath + "/notification" },
    profile: {
      key: complianceManagerNonlifeBasePath + "/profile",
    },
    ticketUtilities: {
      key: complianceManagerNonlifeBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: complianceManagerNonlifeBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: complianceManagerNonlifeBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: complianceManagerNonlifeBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: complianceManagerNonlifeBasePath + "/ticket-utilities/devices-system",
    },
  },
  INFRASTRUCTURE_DATA_CENTER_MANAGER: {
    dashboard: {
      key: infrastructureDataCenterManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: infrastructureDataCenterManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: infrastructureDataCenterManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: infrastructureDataCenterManagerBasePath + "/request-form/:id",
      parse: (id: string) => infrastructureDataCenterManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: infrastructureDataCenterManagerBasePath + "/notification" },
    profile: {
      key: infrastructureDataCenterManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: infrastructureDataCenterManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: infrastructureDataCenterManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: infrastructureDataCenterManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: infrastructureDataCenterManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: infrastructureDataCenterManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  DIGITAL_MEDIA_ASSISTANT: {
    dashboard: {
      key: digitalMediaAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: digitalMediaAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: digitalMediaAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: digitalMediaAssistantBasePath + "/request-form/:id",
      parse: (id: string) => digitalMediaAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: digitalMediaAssistantBasePath + "/notification" },
    profile: {
      key: digitalMediaAssistantBasePath + "/profile",
    },
  },
  PROGRAM_MANAGER: {
    dashboard: {
      key: programManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: programManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: programManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: programManagerBasePath + "/request-form/:id",
      parse: (id: string) => programManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: programManagerBasePath + "/notification" },
    profile: {
      key: programManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: programManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: programManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: programManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: programManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: programManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  DATA_WAREHOUSING_ASSISTANT: {
    dashboard: {
      key: dataWarehousingAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: dataWarehousingAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: dataWarehousingAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: dataWarehousingAssistantBasePath + "/request-form/:id",
      parse: (id: string) => dataWarehousingAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: dataWarehousingAssistantBasePath + "/notification" },
    profile: {
      key: dataWarehousingAssistantBasePath + "/profile",
    },
  },
  DATA_QUALITY_MANAGEMENT_SPECIALIST: {
    dashboard: {
      key: dataQualityManagementSpecialistBasePath + "/dashboard",
    },
    requestDashboard: {
      key: dataQualityManagementSpecialistBasePath + "/request-dashboard",
    },
    requestForm: {
      key: dataQualityManagementSpecialistBasePath + "/request-form",
    },
    viewRequestForm: {
      key: dataQualityManagementSpecialistBasePath + "/request-form/:id",
      parse: (id: string) => dataQualityManagementSpecialistBasePath + `/request-form/${id}`,
    },
    notification: { key: dataQualityManagementSpecialistBasePath + "/notification" },
    profile: {
      key: dataQualityManagementSpecialistBasePath + "/profile",
    },
  },
  CLAIMS_SPECIALIST: {
    dashboard: {
      key: claimsSpecialistBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsSpecialistBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsSpecialistBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsSpecialistBasePath + "/request-form/:id",
      parse: (id: string) => claimsSpecialistBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsSpecialistBasePath + "/notification" },
    profile: {
      key: claimsSpecialistBasePath + "/profile",
    },
  },
  NON_LIFE_CLAIMS_CLERK: {
    dashboard: {
      key: nonLifeClaimsClerkBasePath + "/dashboard",
    },
    requestDashboard: {
      key: nonLifeClaimsClerkBasePath + "/request-dashboard",
    },
    requestForm: {
      key: nonLifeClaimsClerkBasePath + "/request-form",
    },
    viewRequestForm: {
      key: nonLifeClaimsClerkBasePath + "/request-form/:id",
      parse: (id: string) => nonLifeClaimsClerkBasePath + `/request-form/${id}`,
    },
    notification: { key: nonLifeClaimsClerkBasePath + "/notification" },
    profile: {
      key: nonLifeClaimsClerkBasePath + "/profile",
    },
  },
  HR_MANAGER: {
    dashboard: {
      key: hrManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: hrManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: hrManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: hrManagerBasePath + "/request-form/:id",
      parse: (id: string) => hrManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: hrManagerBasePath + "/notification" },
    profile: {
      key: hrManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: hrManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: hrManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: hrManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: hrManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: hrManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  BOOKKEEPER_1: {
    dashboard: {
      key: bookkeeper1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: bookkeeper1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: bookkeeper1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: bookkeeper1BasePath + "/request-form/:id",
      parse: (id: string) => bookkeeper1BasePath + `/request-form/${id}`,
    },
    notification: { key: bookkeeper1BasePath + "/notification" },
    profile: {
      key: bookkeeper1BasePath + "/profile",
    },
  },
  ACTUARIAL_ASSISTANT: {
    dashboard: {
      key: actuarialAssistantBasePath + "/dashboard",
    },
    requestDashboard: {
      key: actuarialAssistantBasePath + "/request-dashboard",
    },
    requestForm: {
      key: actuarialAssistantBasePath + "/request-form",
    },
    viewRequestForm: {
      key: actuarialAssistantBasePath + "/request-form/:id",
      parse: (id: string) => actuarialAssistantBasePath + `/request-form/${id}`,
    },
    notification: { key: actuarialAssistantBasePath + "/notification" },
    profile: {
      key: actuarialAssistantBasePath + "/profile",
    },
  },
  AVP_FINANCE: {
    dashboard: {
      key: avpFinanceBasePath + "/dashboard",
    },
    requestDashboard: {
      key: avpFinanceBasePath + "/request-dashboard",
    },
    requestForm: {
      key: avpFinanceBasePath + "/request-form",
    },
    viewRequestForm: {
      key: avpFinanceBasePath + "/request-form/:id",
      parse: (id: string) => avpFinanceBasePath + `/request-form/${id}`,
    },
    notification: { key: avpFinanceBasePath + "/notification" },
    profile: {
      key: avpFinanceBasePath + "/profile",
    },
  },
  ACTUARY_ASSISTANT_RELIEVER: {
    dashboard: {
      key: actuaryAssistantRelieverBasePath + "/dashboard",
    },
    requestDashboard: {
      key: actuaryAssistantRelieverBasePath + "/request-dashboard",
    },
    requestForm: {
      key: actuaryAssistantRelieverBasePath + "/request-form",
    },
    viewRequestForm: {
      key: actuaryAssistantRelieverBasePath + "/request-form/:id",
      parse: (id: string) => actuaryAssistantRelieverBasePath + `/request-form/${id}`,
    },
    notification: { key: actuaryAssistantRelieverBasePath + "/notification" },
    profile: {
      key: actuaryAssistantRelieverBasePath + "/profile",
    },
  },
  CUSTOMER_SERVICE_ACCT_RETENTION_SPECIALIST: {
    dashboard: {
      key: customerServiceAcctRetentionSpecialistBasePath + "/dashboard",
    },
    requestDashboard: {
      key: customerServiceAcctRetentionSpecialistBasePath + "/request-dashboard",
    },
    requestForm: {
      key: customerServiceAcctRetentionSpecialistBasePath + "/request-form",
    },
    viewRequestForm: {
      key: customerServiceAcctRetentionSpecialistBasePath + "/request-form/:id",
      parse: (id: string) => customerServiceAcctRetentionSpecialistBasePath + `/request-form/${id}`,
    },
    notification: { key: customerServiceAcctRetentionSpecialistBasePath + "/notification" },
    profile: {
      key: customerServiceAcctRetentionSpecialistBasePath + "/profile",
    },
  },
  REINSURANCE_ASST: {
    dashboard: {
      key: reinsuranceAsstBasePath + "/dashboard",
    },
    requestDashboard: {
      key: reinsuranceAsstBasePath + "/request-dashboard",
    },
    requestForm: {
      key: reinsuranceAsstBasePath + "/request-form",
    },
    viewRequestForm: {
      key: reinsuranceAsstBasePath + "/request-form/:id",
      parse: (id: string) => reinsuranceAsstBasePath + `/request-form/${id}`,
    },
    notification: { key: reinsuranceAsstBasePath + "/notification" },
    profile: {
      key: reinsuranceAsstBasePath + "/profile",
    },
  },
  ASSISTANT_DIVISION_MANAGER_NL: {
    dashboard: {
      key: assistantDivisionManagerNlBasePath + "/dashboard",
    },
    requestDashboard: {
      key: assistantDivisionManagerNlBasePath + "/request-dashboard",
    },
    requestForm: {
      key: assistantDivisionManagerNlBasePath + "/request-form",
    },
    viewRequestForm: {
      key: assistantDivisionManagerNlBasePath + "/request-form/:id",
      parse: (id: string) => assistantDivisionManagerNlBasePath + `/request-form/${id}`,
    },
    notification: { key: assistantDivisionManagerNlBasePath + "/notification" },
    profile: {
      key: assistantDivisionManagerNlBasePath + "/profile",
    },
  },
  CLAIMS_EVALUATOR_PROCESSOR: {
    dashboard: {
      key: claimsEvaluatorProcessorBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsEvaluatorProcessorBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsEvaluatorProcessorBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsEvaluatorProcessorBasePath + "/request-form/:id",
      parse: (id: string) => claimsEvaluatorProcessorBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsEvaluatorProcessorBasePath + "/notification" },
    profile: {
      key: claimsEvaluatorProcessorBasePath + "/profile",
    },
  },
  MEMBER_RELATIONS_MANAGER: {
    dashboard: {
      key: memberRelationsManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: memberRelationsManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: memberRelationsManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: memberRelationsManagerBasePath + "/request-form/:id",
      parse: (id: string) => memberRelationsManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: memberRelationsManagerBasePath + "/notification" },
    profile: {
      key: memberRelationsManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: memberRelationsManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: memberRelationsManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: memberRelationsManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: memberRelationsManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: memberRelationsManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  UNDERWRITING_FILING_CLERK_CLIMBS_CARES_STAFF: {
    dashboard: {
      key: underwritingFilingClerkClimbsCaresStaffBasePath + "/dashboard",
    },
    requestDashboard: {
      key: underwritingFilingClerkClimbsCaresStaffBasePath + "/request-dashboard",
    },
    requestForm: {
      key: underwritingFilingClerkClimbsCaresStaffBasePath + "/request-form",
    },
    viewRequestForm: {
      key: underwritingFilingClerkClimbsCaresStaffBasePath + "/request-form/:id",
      parse: (id: string) => underwritingFilingClerkClimbsCaresStaffBasePath + `/request-form/${id}`,
    },
    notification: { key: underwritingFilingClerkClimbsCaresStaffBasePath + "/notification" },
    profile: {
      key: underwritingFilingClerkClimbsCaresStaffBasePath + "/profile",
    },
  },
  ADMINISTRATIVE_ASSISTANT_1_OOP: {
    dashboard: {
      key: administrativeAssistant1OopBasePath + "/dashboard",
    },
    requestDashboard: {
      key: administrativeAssistant1OopBasePath + "/request-dashboard",
    },
    requestForm: {
      key: administrativeAssistant1OopBasePath + "/request-form",
    },
    viewRequestForm: {
      key: administrativeAssistant1OopBasePath + "/request-form/:id",
      parse: (id: string) => administrativeAssistant1OopBasePath + `/request-form/${id}`,
    },
    notification: { key: administrativeAssistant1OopBasePath + "/notification" },
    profile: {
      key: administrativeAssistant1OopBasePath + "/profile",
    },
  },
  OCCUPATIONAL_HEALTH_NURSE: {
    dashboard: {
      key: occupationalHealthNurseBasePath + "/dashboard",
    },
    requestDashboard: {
      key: occupationalHealthNurseBasePath + "/request-dashboard",
    },
    requestForm: {
      key: occupationalHealthNurseBasePath + "/request-form",
    },
    viewRequestForm: {
      key: occupationalHealthNurseBasePath + "/request-form/:id",
      parse: (id: string) => occupationalHealthNurseBasePath + `/request-form/${id}`,
    },
    notification: { key: occupationalHealthNurseBasePath + "/notification" },
    profile: {
      key: occupationalHealthNurseBasePath + "/profile",
    },
  },
  PARALEGAL: {
    dashboard: {
      key: paralegalBasePath + "/dashboard",
    },
    requestDashboard: {
      key: paralegalBasePath + "/request-dashboard",
    },
    requestForm: {
      key: paralegalBasePath + "/request-form",
    },
    viewRequestForm: {
      key: paralegalBasePath + "/request-form/:id",
      parse: (id: string) => paralegalBasePath + `/request-form/${id}`,
    },
    notification: { key: paralegalBasePath + "/notification" },
    profile: {
      key: paralegalBasePath + "/profile",
    },
  },
  ADMIN_ENVIRONMENTAL_ASSISTANT_1: {
    dashboard: {
      key: adminEnvironmentalAssistant1BasePath + "/dashboard",
    },
    requestDashboard: {
      key: adminEnvironmentalAssistant1BasePath + "/request-dashboard",
    },
    requestForm: {
      key: adminEnvironmentalAssistant1BasePath + "/request-form",
    },
    viewRequestForm: {
      key: adminEnvironmentalAssistant1BasePath + "/request-form/:id",
      parse: (id: string) => adminEnvironmentalAssistant1BasePath + `/request-form/${id}`,
    },
    notification: { key: adminEnvironmentalAssistant1BasePath + "/notification" },
    profile: {
      key: adminEnvironmentalAssistant1BasePath + "/profile",
    },
  },
  CHIEF_INTERNAL_AUDITOR: {
    dashboard: {
      key: chiefInternalAuditorBasePath + "/dashboard",
    },
    requestDashboard: {
      key: chiefInternalAuditorBasePath + "/request-dashboard",
    },
    requestForm: {
      key: chiefInternalAuditorBasePath + "/request-form",
    },
    viewRequestForm: {
      key: chiefInternalAuditorBasePath + "/request-form/:id",
      parse: (id: string) => chiefInternalAuditorBasePath + `/request-form/${id}`,
    },
    notification: { key: chiefInternalAuditorBasePath + "/notification" },
    profile: {
      key: chiefInternalAuditorBasePath + "/profile",
    },
  },
  REINSURANCE_UNDERWRITING_MANAGER: {
    dashboard: {
      key: reinsuranceUnderwritingManagerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: reinsuranceUnderwritingManagerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: reinsuranceUnderwritingManagerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: reinsuranceUnderwritingManagerBasePath + "/request-form/:id",
      parse: (id: string) => reinsuranceUnderwritingManagerBasePath + `/request-form/${id}`,
    },
    notification: { key: reinsuranceUnderwritingManagerBasePath + "/notification" },
    profile: {
      key: reinsuranceUnderwritingManagerBasePath + "/profile",
    },
    ticketUtilities: {
      key: reinsuranceUnderwritingManagerBasePath + "/ticket-utilities",
    },
    requestTypesUtility: {
      key: reinsuranceUnderwritingManagerBasePath + "/ticket-utilities/request-type",
    },
    operatingSystemsUtility: {
      key: reinsuranceUnderwritingManagerBasePath + "/ticket-utilities/operating-system",
    },
    applicationsUtility: {
      key: reinsuranceUnderwritingManagerBasePath + "/ticket-utilities/applications",
    },
    devicesSystemUtility: {
      key: reinsuranceUnderwritingManagerBasePath + "/ticket-utilities/devices-system",
    },
  },
  PROJECT_RESEARCH_MEL_OFFICER: {
    dashboard: {
      key: projectResearchMelOfficerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: projectResearchMelOfficerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: projectResearchMelOfficerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: projectResearchMelOfficerBasePath + "/request-form/:id",
      parse: (id: string) => projectResearchMelOfficerBasePath + `/request-form/${id}`,
    },
    notification: { key: projectResearchMelOfficerBasePath + "/notification" },
    profile: {
      key: projectResearchMelOfficerBasePath + "/profile",
    },
  },
  BILLING_COLLECTIONS: {
    dashboard: {
      key: billingCollectionsBasePath + "/dashboard",
    },
    requestDashboard: {
      key: billingCollectionsBasePath + "/request-dashboard",
    },
    requestForm: {
      key: billingCollectionsBasePath + "/request-form",
    },
    viewRequestForm: {
      key: billingCollectionsBasePath + "/request-form/:id",
      parse: (id: string) => billingCollectionsBasePath + `/request-form/${id}`,
    },
    notification: { key: billingCollectionsBasePath + "/notification" },
    profile: {
      key: billingCollectionsBasePath + "/profile",
    },
  },

  SALES_DEVELOPMENT_SPECIALIST: {
    dashboard: {
      key: salesDevelopmentSpecialistBasePath + "/dashboard",
    },
    requestDashboard: {
      key: salesDevelopmentSpecialistBasePath + "/request-dashboard",
    },
    requestForm: {
      key: salesDevelopmentSpecialistBasePath + "/request-form",
    },
    viewRequestForm: {
      key: salesDevelopmentSpecialistBasePath + "/request-form/:id",
      parse: (id: string) => salesDevelopmentSpecialistBasePath + `/request-form/${id}`,
    },
    notification: { key: salesDevelopmentSpecialistBasePath + "/notification" },
    profile: {
      key: salesDevelopmentSpecialistBasePath + "/profile",
    },
  },
  BANK_RECON_SPECIALIST: {
    dashboard: {
      key: bankReconSpecialistBasePath + "/dashboard",
    },
    requestDashboard: {
      key: bankReconSpecialistBasePath + "/request-dashboard",
    },
    requestForm: {
      key: bankReconSpecialistBasePath + "/request-form",
    },
    viewRequestForm: {
      key: bankReconSpecialistBasePath + "/request-form/:id",
      parse: (id: string) => bankReconSpecialistBasePath + `/request-form/${id}`,
    },
    notification: { key: bankReconSpecialistBasePath + "/notification" },
    profile: {
      key: bankReconSpecialistBasePath + "/profile",
    },
  },
  CHIEF_OF_STAFF: {
    dashboard: {
      key: chiefOfStaffBasePath + "/dashboard",
    },
    requestDashboard: {
      key: chiefOfStaffBasePath + "/request-dashboard",
    },
    requestForm: {
      key: chiefOfStaffBasePath + "/request-form",
    },
    viewRequestForm: {
      key: chiefOfStaffBasePath + "/request-form/:id",
      parse: (id: string) => chiefOfStaffBasePath + `/request-form/${id}`,
    },
    notification: { key: chiefOfStaffBasePath + "/notification" },
    profile: {
      key: chiefOfStaffBasePath + "/profile",
    },
  },
  UNDERWRITING_ASSISTANT_NL: {
    dashboard: {
      key: underwritingAssistantNlBasePath + "/dashboard",
    },
    requestDashboard: {
      key: underwritingAssistantNlBasePath + "/request-dashboard",
    },
    requestForm: {
      key: underwritingAssistantNlBasePath + "/request-form",
    },
    viewRequestForm: {
      key: underwritingAssistantNlBasePath + "/request-form/:id",
      parse: (id: string) => underwritingAssistantNlBasePath + `/request-form/${id}`,
    },
    notification: { key: underwritingAssistantNlBasePath + "/notification" },
    profile: {
      key: underwritingAssistantNlBasePath + "/profile",
    },
  },
  DATA_PROCESSOR: {
    dashboard: {
      key: dataProcessorBasePath + "/dashboard",
    },
    requestDashboard: {
      key: dataProcessorBasePath + "/request-dashboard",
    },
    requestForm: {
      key: dataProcessorBasePath + "/request-form",
    },
    viewRequestForm: {
      key: dataProcessorBasePath + "/request-form/:id",
      parse: (id: string) => dataProcessorBasePath + `/request-form/${id}`,
    },
    notification: { key: dataProcessorBasePath + "/notification" },
    profile: {
      key: dataProcessorBasePath + "/profile",
    },
  },
  ADMIN_OFFICER: {
    dashboard: {
      key: adminOfficerBasePath + "/dashboard",
    },
    requestDashboard: {
      key: adminOfficerBasePath + "/request-dashboard",
    },
    requestForm: {
      key: adminOfficerBasePath + "/request-form",
    },
    viewRequestForm: {
      key: adminOfficerBasePath + "/request-form/:id",
      parse: (id: string) => adminOfficerBasePath + `/request-form/${id}`,
    },
    notification: { key: adminOfficerBasePath + "/notification" },
    profile: {
      key: adminOfficerBasePath + "/profile",
    },
  },
  CLAIMS_ASSISTANT_NL: {
    dashboard: {
      key: claimsAssistantNlBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsAssistantNlBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsAssistantNlBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsAssistantNlBasePath + "/request-form/:id",
      parse: (id: string) => claimsAssistantNlBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsAssistantNlBasePath + "/notification" },
    profile: {
      key: claimsAssistantNlBasePath + "/profile",
    },
  },
  CLAIMS_ANALYST: {
    dashboard: {
      key: claimsAnalystBasePath + "/dashboard",
    },
    requestDashboard: {
      key: claimsAnalystBasePath + "/request-dashboard",
    },
    requestForm: {
      key: claimsAnalystBasePath + "/request-form",
    },
    viewRequestForm: {
      key: claimsAnalystBasePath + "/request-form/:id",
      parse: (id: string) => claimsAnalystBasePath + `/request-form/${id}`,
    },
    notification: { key: claimsAnalystBasePath + "/notification" },
    profile: {
      key: claimsAnalystBasePath + "/profile",
    },
  },
  CASHIER_NL: {
    dashboard: {
      key: cashierNlBasePath + "/dashboard",
    },
    requestDashboard: {
      key: cashierNlBasePath + "/request-dashboard",
    },
    requestForm: {
      key: cashierNlBasePath + "/request-form",
    },
    viewRequestForm: {
      key: cashierNlBasePath + "/request-form/:id",
      parse: (id: string) => cashierNlBasePath + `/request-form/${id}`,
    },
    notification: { key: cashierNlBasePath + "/notification" },
    profile: {
      key: cashierNlBasePath + "/profile",
    },
  },
};

import httpClient from "@clients/httpClient";
import { IDefaultParams } from "@interface/common.interface";
import {
  IProductProposalApprovalUpdateStatusPayload,
  IProductProposalCommissionApproval,
  IProductProposalNotaryUpdateStatusPayload,
  IProductProposalUpdateStatusPayload,
} from "@interface/product-proposal.interface";
import { IUpdateProductProposalStatusPayload, TCooperativePayload, TProductProposalPayload } from "@state/types/product-proposal";

import { IProductProposalCommissionPayload } from "@interface/product-proposal.interface";
import { UserRoles } from "@interface/routes.interface";
import { formatStringAtoZ0to9 } from "@helpers/text";
import { ProposalStatus } from "@enums/proposal-status";

const apiResource = "product-proposals"; // Define the API resource endpoint for Products
// for future use
// const relations = "relations=proposable|proposable.product.productRevisions.productGuidelines|cooperative|proposable.product.productType";
// "relations=proposable|proposable.productGuidelines|proposable.commission|proposable.commission.commissionDetails|proposable.commission.commissionDetails.commissionType|proposable.commission.commissionDetails.commissionAgeType|proposable.signatories.user|proposable.signatories.user.position|proposable.product|proposable.product.productType|attachments|cooperative|proposalNotarization.attachments|requirementable.requirements|requirementable.requirements|proposable.productRevisionGuidelinesTagSequence|proposable.productRevisionGuidelinesTagSequence.productRevisionGuidelineTag";
const relationsv2 =
  "cooperative|product|proposable|proposable.product|proposable.productGuidelines|proposable.commission|commissionStructure|proposable.commission.commissionDetails|proposable.commission.commissionDetails.commissionType|proposable.commission.commissionDetails.commissionAgeType|proposable.signatories.user|proposable.signatories.user.position|proposable.product|proposable.product.productType|attachments|cooperative|proposalNotarization.attachments|requirementable.requirements|proposable.productRevisionGuidelinesTagSequence|proposable.productRevisionGuidelinesTagSequence.productRevisionGuidelineTag&relations=product|cooperative|product.productType|proposalApproval|commissionStructure.approval.signatories.user.position|requirementable.requirements&sort=id,desc"; // need help jericho data need requirementable, commission Structure data
const relationsSignatories = "&sort=id,desc&relations=product|cooperative|commissionStructure.approval.signatories.user.position|requirementable.requirements";
const coopRelations = "relations=cooperativeType|cooperativeAffiliations.affiliation|cooperativeOfficers.position|cooperativeCategory";
const relationsv3 =
  "relations=product|product.productType|cooperative|cooperative.cooperativeAffiliations.affiliation|cooperative.cooperativeOfficers.position|proposalApproval|proposalNotarization|proposalAgreement|commissionStructure.commissionDetails.commissionType|requirementable.requirements|commissionStructure.approval.signatories.user.position";

const relationsgetProposalV1 =
  "relations=proposalApproval|proposalApproval.attachments|proposalAgreement|proposalAgreement.attachments|product|attachments|cooperative|cooperative.cooperativeCategory|cooperative.cooperativeType|cooperative.cooperativeAffiliations.affiliation|cooperative.cooperativeOfficers|commissionStructure|commissionStructure.commissionDetails|commissionStructure.commissionDetails.commissionType|commissionStructure.approval.signatories.user.position|proposalNotarization.attachments|proposalAgreement.attachments|requirementable.requirements.attachments|cooperative.cooperativeOfficers.position|proposable.productGuidelines|proposable.commission|proposable.commission.commissionDetails|proposable.commission.commissionDetails.commissionType|proposable.commission.commissionDetails.commissionAgeType|proposable.product.productRevisions.commission|proposable.product.productRevisions.commission.commissionDetails|proposable.product.productRevisions.commission.commissionDetails.commissionType|proposable.product.productRevisions.commission.commissionDetails.commissionAgeType"; //product

const relationsgetProposalV2 =
  "relations=proposalApproval|proposalApproval.attachments|proposalAgreement|proposalAgreement.attachments|product|attachments|cooperative|cooperative.cooperativeCategory|cooperative.cooperativeType|cooperative.cooperativeAffiliations.affiliation|cooperative.cooperativeOfficers|commissionStructure|commissionStructure.commissionDetails|commissionStructure.commissionDetails.commissionType|commissionStructure.approval.signatories.user.position|proposalNotarization.attachments|proposalAgreement.attachments|requirementable.requirements.attachments|cooperative.cooperativeOfficers.position|proposable.quotation|proposable.product.productRevisions.productGuidelines|proposable.product.productRevisions.productGuidelines|proposable.quotation.quotationCommissionDistribution|proposable.quotation.quotationCommissionDistribution.commissionType|proposable.product.productRevisions.commission|proposable.product.productRevisions.commission.commissionDetails|proposable.product.productRevisions.commission.commissionDetails.commissionType|proposable.product.productRevisions.commission.commissionDetails.commissionAgeType|proposable.quotation.clspBenefits|proposable.quotation.clppBenefits|proposable.quotation.gyrtBenefits|proposable.quotation.fipPrincipalMember|proposable.quotation.fipPrincipalMemberBenefit|proposable.quotation.fipCoInsuredDependent|proposable.quotation.fipCoInsuredDependentBenefit";

// Function to fetch product proposals
export const getProductProposalsService = async (params: IDefaultParams) => {
  // Perform a GET request to fetch product proposals with filters
  let query = `${apiResource}?pageSize=${params.pageSize ?? 100}&page=${params.page ?? 1}`;

  if (params.filter) {
    query += `&product.name[like]=${params.filter}`;
  }
  //Filter  by status
  if (params.statusFilter) {
    query += `&status[eq]=${params.statusFilter}`;
  }
  //Filter by date
  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }
  //Filter by product type
  if (params?.productTypeFilter) {
    query += `&product.productTypeId[eq]=${params.productTypeFilter}`;
  }
  //Filter by product category
  if (params.condition) {
    query += `&${params.condition}`;
  }
  if (params.showArchived) {
    // When showing archived, only show ARCHIVED status
    query += `&status[eq]=${ProposalStatus.archived}`;
  } else {
    // When not showing archived, exclude ARCHIVED status and apply other status filters
    query += `&status[neq]=${ProposalStatus.archived}`;

    // Apply additional status filter only when not showing archived
    if (params.statusFilter) {
      query += `&status[eq]=${params.statusFilter}`;
    }
  }
  if (params?.user) {
    query += `&createdBy[eq]=${params?.user}`;
  }

  return httpClient.get(`${query}&${relationsv3}&sort=id,desc`);
};

// Function to post product proposals
export const postProductProposalService = async (payload: TProductProposalPayload) => {
  // Perform a POST request to create a new product proposal
  return httpClient.post(`${apiResource}`, payload);
};

// Function to put product proposals
export const putProductProposalService = async (payload: TProductProposalPayload, proposalId: number | string) => {
  // Perform a PUT request to update a product proposal
  return httpClient.post(`${apiResource}/${proposalId}`, payload);
};

// Function to delete product proposals
export const deleteProductProposalService = async (proposalId: number | string) => {
  // Perform a PUT request to update a product proposal
  return httpClient.delete(`${apiResource}/${proposalId}`);
};

// Function to fetch cooperatives
export const getCooperativesService = async (payload: IDefaultParams) => {
  // Perform a GET request to fetch cooperatives with filters
  return httpClient.get(`cooperatives?${payload.filter ? "coopName[like]=" + payload.filter + "&" : ""}pageSize=${payload.pageSize ?? 100}&page=${payload.page ?? 1}&${coopRelations}`);
};

// Function to create a cooperative
export const postCooperativeService = async (payload: TCooperativePayload) => {
  // Perform a POST request to create a cooperative
  return httpClient.post("cooperatives", payload);
};

// Function to update a cooperative
export const putCooperativeService = async (payload: TCooperativePayload) => {
  // Perform a PUT request to update a cooperative
  return httpClient.put(`cooperatives/${payload.id}`, payload);
};

// Function to fetch cda cooperatives
export const getCDACooperativesService = async (payload: IDefaultParams) => {
  // Perform a GET request to fetch cda cooperatives with filters
  return httpClient.get(`cooperatives-cda?${payload.filter ? "name[like]=" + payload.filter + "&" : ""}pageSize=${payload.pageSize ?? 100}&page=${payload.page ?? 1}`);
};

export const postProductProposalCommissionService = async (payload: IProductProposalCommissionPayload) => {
  return httpClient.post(`${apiResource}/${payload.productProposalId}/commission-structure`, payload);
};

// Function to check cooperative if exist
export const checkCooperativeIfExist = async (coop: string) => {
  return httpClient.get(`cooperatives?coopName[like]=${coop}`);
};

export const getSignatoriesLogsService = async (id?: number) => {
  return httpClient.get(`shared/approvals/${id}/logs?relations=causer|causer.position&sort=id,desc`);
};

export const getProductProposalsNotarizationsService = async (payload: IDefaultParams) => {
  let query = `${apiResource}/sales-executive?relations=product|attachments|proposalNotarization|cooperative|proposalAgreement.attachments&product.name[like]=&sort=id,desc`;

  if (payload?.filter) {
    query += `&product.name[like]=${payload.filter}`;
  }
  if (payload?.statusFilter) {
    query += `&proposalNotarization.agreementNotarizationStatus[eq]=${payload.statusFilter}`;
  }
  if (payload?.dateFrom && payload?.dateTo) {
    query += `&proposalNotarization.agreementNotarizationDate[between]=${payload.dateFrom},${payload.dateTo}`;
  }
  return httpClient.get(`${query}&sort=id,desc`);
};

// Function to post product proposals notarizations
export const postProductProposalsNotarizationsService = async (id?: number, payload?: IProductProposalNotaryUpdateStatusPayload) => {
  const config = {
    headers: {
      "Content-type": "multipart/form-data;",
    },
  };
  return httpClient.post(`${apiResource}/${id}/notarizations`, payload, config);
};

export const getMarketingProductProposalsService = async (params: IDefaultParams) => {
  // Perform a GET request to fetch product proposals with filters
  let query = `${apiResource}?pageSize=${params.pageSize ?? 100}&page=${params.page ?? 1}`;
  if (params.type) {
    if (params.type === formatStringAtoZ0to9(UserRoles.marketing)) {
      // query += `&marketing${relationsv2}`;
      query += `&${relationsv2}`;
    } else {
      query += `${relationsSignatories}`;
    }
  } else {
    // Use relationsSignatories if params.type is not marketing or not declared
    query += `${relationsSignatories}`;
  }
  if (params.filter) {
    query += `&product.name[like]=${params.filter}`;
  }
  if (params.condition) {
    query += `&${params.condition}`;
  }
  //gamiton nku para mag filter sa user
  if (params?.nameFilter) {
    query += `${params.nameFilter}`;
  }

  //return httpClient.get(`${query}&${relationsv2}&sort=id,desc&relations=product|cooperative`); //for backup
  return httpClient.get(`${query}`);
};

export const getProductProposalLogService = async (proposalId: string) => {
  return httpClient.get(`${apiResource}/${proposalId}/activity-logs`);
};

//UNDERWRITING
const provisionsApiResource = "/product-proposal-approvals";
export const getUnderwritingProductProposalProvisionByIdService = async (id: number) => {
  return httpClient.get(
    `${provisionsApiResource}/underwriting?id[eq]=${id}&relations=createdBy|proposable|product|proposable.productGuidelines|proposable.commission|proposable.commission.commissionDetails|proposable.commission.commissionDetails.commissionType|proposable.commission.commissionDetails.commissionAgeType|proposable.signatories.user|proposable.signatories.user.position|proposable.product|proposable.product.productType|attachments|cooperative|proposalNotarization.attachments|requirementable.requirements|requirementable.requirements|provisionApproval.user.position`
  );
};
export const getUnderwritingProductProposalProvisionsService = async (params: IDefaultParams) => {
  // Perform a GET request to fetch product proposals with filters
  let query = `${provisionsApiResource}/underwriting?pageSize=${params.pageSize ?? 100}&page=${params.page ?? 1}`;

  if (params.filter) {
    query += `&product.name[like]=${params.filter}`;
  }
  //Filter by status
  if (params.statusFilter) {
    query += `&status[eq]=${params.statusFilter}`;
  }
  //Filter by date
  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  return httpClient.get(`${query}&relations=provisionApproval|product|cooperative|proposable.product|createdBy|createdBy.position|product.productType|provisionApproval.user.position&sort=id,desc`);
};

export const postUnderwritingProductProposalProvisionService = async (payload: IProductProposalApprovalUpdateStatusPayload) => {
  return httpClient.post(`${provisionsApiResource}/underwriting`, payload);
};

//CLAIMS
export const getClaimsProductProposalProvisionByIdService = async (id: number) => {
  return httpClient.get(
    `${provisionsApiResource}/claims?id[eq]=${id}&relations=proposable|proposable.productGuidelines|proposable.commission|proposable.commission.commissionDetails|proposable.commission.commissionDetails.commissionType|proposable.commission.commissionDetails.commissionAgeType|proposable.signatories.user|proposable.signatories.user.position|product|proposable.product.productType|attachments|cooperative|proposalNotarization.attachments|requirementable.requirements|requirementable.requirements|provisionApproval.user.position|createdBy`
  );
};
export const getClaimsProductProposalProvisionsService = async (params: IDefaultParams) => {
  // Perform a GET request to fetch product proposals with filters
  let query = `${provisionsApiResource}/claims?pageSize=${params.pageSize ?? 100}&page=${params.page ?? 1}`;

  if (params.filter) {
    query += `&product.name[like]=${params.filter}`;
  }
  //Filter by status
  if (params.statusFilter) {
    query += `&status[eq]=${params.statusFilter}`;
  }
  //Filter by date
  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  return httpClient.get(
    // `${query}&relations=provisionApproval|product|cooperative|proposable.product|proposable.commission|product.productType|createdBy|provisionApproval.user.position&sort=id,desc`
    `${query}&relations=provisionApproval|product|cooperative|proposable|product.productType|createdBy|provisionApproval.user.position&sort=id,desc`
  );
};

export const postClaimsProductProposalProvisionService = async (payload: IProductProposalUpdateStatusPayload) => {
  return httpClient.post(`${provisionsApiResource}/claims`, payload);
};
// export const getProposalService = async (id?: string | number, type?: "standard" | "custom") => {
export const getProposalService = async (params: IDefaultParams) => {
  let query = `${apiResource}`;

  if (params.type) {
    if (params.type === "custom") {
      query += `?${relationsgetProposalV2}`;
    } else if (params.type === "standard") {
      query += `?${relationsgetProposalV1}`;
    }
  } else {
    // Default relations if params.type is not declared
    query += `?${relationsgetProposalV1}`;
  }
  if (params.page) {
    query += `&page=${params.page}`;
  }
  if (params.pageSize) {
    query += `&pageSize=${params.pageSize}`;
  }
  if (params.id) {
    query += `&cooperative.id[eq]=${params.id}`;
  }
  if (params.nameFilter) {
    query += `&product.productCode[eq]=${params.nameFilter}`;
  }
  if (params.statusFilter) {
    query += `&status[eq]=${params.statusFilter}`;
  }
  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }
  query += `&pageSize=${params.pageSize ?? 100}&page=${params.page ?? 1}&sort=id,desc`;

  return httpClient.get(query);
};
export const postProductProposalCommissionApprovalService = async (payload: IProductProposalCommissionApproval) => {
  return httpClient.post(`shared/approvals`, payload);
};
// Function to put Product Proposal Commission
export const putProductProposalCommissionService = async (payload: { managementPercentFee: number }, proposalId: number | string) => {
  return httpClient.post(`${apiResource}/management-percentage/${proposalId}/edit`, payload);
};

export const updateProductProposalStatusService = async (payload: IUpdateProductProposalStatusPayload) => {
  return httpClient.patch(`${apiResource}/${payload.id}/status`, {
    status: payload.status,
  });
};

import httpClient from "@clients/httpClient";
import {
  TUtilitiesRegionPayload,
  TUtilitiesRegionWithIDAndIndexPayload,
} from "@state/types/utilities-region";

import { IDefaultParams } from "@interface/common.interface";

const apiResource = "region";

export const getRegionsService = async (params: IDefaultParams) => {
  let queryParams = "";

  if (params.page) {
    queryParams += `&page=${params.page}`;
  }
  if (params.pageSize) {
    queryParams += `&pageSize=${params.pageSize}`;
  }
  if (params.filter) {
    queryParams += `&regionName[like]=${params.filter}`;
  }
  return httpClient.get(`${apiResource}? ${queryParams}`);
};

export const getRegionService = async (id: number) => {
  return httpClient.get(`${apiResource}/${id}`);
};

export const postRegionService = async (payload: TUtilitiesRegionPayload) => {
  return httpClient.post(`${apiResource}`, payload);
};

export const putRegionService = async (payload: TUtilitiesRegionPayload) => {
  return httpClient.put(`${apiResource}/${payload.id}`, payload);
};

export const destroyRegionService = async (payload: TUtilitiesRegionWithIDAndIndexPayload) => {
  return httpClient.delete(`${apiResource}/${payload.id}`);
};

import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import { FaFile, FaFileAlt } from "react-icons/fa";
import AreaAdminNewForms from "@modules/area-admin/new-forms";
import ViewFormReceiving from "@modules/area-admin/new-forms/components/Forms/ForReceivingView";
import ViewTransmittalForm from "@modules/area-admin/new-forms/components/Forms/TransmittalForm";
import ViewReleasedForms from "@modules/area-admin/new-forms/components/Forms/ViewReleasedForm";
import AreaAdminUsedForms from "@modules/area-admin/used-forms";
import NotificationPage from "@modules/shared/notification";
import ViewReturnedFormReceiving from "@components/template/Forms/UsedForms/ViewReturnedForReceivingForm";
import ViewReturnTransmittalForm from "@modules/area-admin/used-forms/components/Forms/TransmittalForm";
import ViewReturnedForm from "@components/template/Forms/UsedForms/ViewReturnedForm";
import Profile from "@modules/shared/profile";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.AREAADMIN.areaAdminDashboard.key,
  path: ROUTES.AREAADMIN.areaAdminDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.areaAdmin],
  icon: MdDashboard,
  isSidebar: true,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.AREAADMIN.notification.key,
  path: ROUTES.AREAADMIN.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const newForm: RouteItem = {
  name: "New Form",
  id: ROUTES.AREAADMIN.areaAdminNewForm.key,
  path: ROUTES.AREAADMIN.areaAdminNewForm.key,
  component: AreaAdminNewForms,
  guard: AuthGuard,
  roles: [UserRoles.areaAdmin],
  icon: FaFile,
  isSidebar: true,
};

export const viewForReceivingForm: RouteItem = {
  name: "View For Receiving Form",
  id: ROUTES.AREAADMIN.viewForReceivingForm.key,
  path: ROUTES.AREAADMIN.viewForReceivingForm.key,
  component: ViewFormReceiving,
  guard: AuthGuard,
  roles: [UserRoles.areaAdmin],
  isSidebar: false,
};

export const transmittalViewForm: RouteItem = {
  name: "View Transmittal Form",
  id: ROUTES.AREAADMIN.viewTransmittalForm.key,
  path: ROUTES.AREAADMIN.viewTransmittalForm.key,
  component: ViewTransmittalForm,
  guard: AuthGuard,
  roles: [UserRoles.areaAdmin],
  isSidebar: false,
};

export const viewReleasedForm: RouteItem = {
  name: "View Released Form",
  id: ROUTES.AREAADMIN.viewReleasedForm.key,
  path: ROUTES.AREAADMIN.viewReleasedForm.key,
  component: ViewReleasedForms,
  guard: AuthGuard,
  roles: [UserRoles.areaAdmin],
  isSidebar: false,
};

export const usedForms: RouteItem = {
  name: "Used Forms",
  id: ROUTES.AREAADMIN.usedForms.key,
  path: ROUTES.AREAADMIN.usedForms.key,
  component: AreaAdminUsedForms,
  guard: AuthGuard,
  roles: [UserRoles.areaAdmin],
  isSidebar: true,
  icon: FaFileAlt,
};

export const viewReturnedReceivingForm: RouteItem = {
  name: "View Returned Receiving Form",
  id: ROUTES.AREAADMIN.viewReturnedReceivingForm.key,
  path: ROUTES.AREAADMIN.viewReturnedReceivingForm.key,
  component: ViewReturnedFormReceiving,
  guard: AuthGuard,
  roles: [UserRoles.areaAdmin],
  isSidebar: false,
};

export const returnTransmittalViewForm: RouteItem = {
  name: "View Return Transmittal Form",
  id: ROUTES.AREAADMIN.viewReturnTransmittalForm.key,
  path: ROUTES.AREAADMIN.viewReturnTransmittalForm.key,
  component: ViewReturnTransmittalForm,
  guard: AuthGuard,
  roles: [UserRoles.areaAdmin],
  isSidebar: false,
};

export const viewReturnedForm: RouteItem = {
  name: "View Returned Form",
  id: ROUTES.AREAADMIN.viewReturnedForm.key,
  path: ROUTES.AREAADMIN.viewReturnedForm.key,
  component: ViewReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.areaAdmin],
  isSidebar: false,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.AREAADMIN.profile.key,
  path: ROUTES.AREAADMIN.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.areaAdmin],
};

export const areaAdminRoutes = [
  overview,
  newForm,
  viewForReceivingForm,
  transmittalViewForm,
  viewReleasedForm,
  usedForms,
  notification,
  viewReturnedReceivingForm,
  returnTransmittalViewForm,
  viewReturnedForm,
  profile,
];

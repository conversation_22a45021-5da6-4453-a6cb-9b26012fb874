import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import { FaClipboardList, FaFile } from "react-icons/fa";
import { TbMessageForward } from "react-icons/tb";
import AdminSatelliteNewForms from "@modules/admin-satellite";
import ViewFormReceiving from "@modules/admin-satellite/components/viewFormReceiving";
import RequestPads from "@modules/admin-satellite/request-pads";
import InventoryTab from "@modules/admin-satellite/inventory";
import TransmittalReturnedForm from "@modules/admin-satellite/components/form/transmittal-returned-form";
import PRTable from "@modules/admin-satellite/components/PRTable";
import IssuePRForm from "@modules/admin-satellite/components/form/issue-pr-form";
import ViewPR from "@modules/admin-satellite/components/form/view-pr";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import ViewReturnedForm from "@components/template/Forms/UsedForms/ViewReturnedForm";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ADMINSATELLITE.adminSatelliteDashboard.key,
  path: ROUTES.ADMINSATELLITE.adminSatelliteDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
  icon: MdDashboard,
  isSidebar: true,
};
export const newForm: RouteItem = {
  name: "New Form",
  id: ROUTES.ADMINSATELLITE.adminSatelliteAdminNewForm.key,
  path: ROUTES.ADMINSATELLITE.adminSatelliteAdminNewForm.key,
  component: AdminSatelliteNewForms,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
  icon: FaFile,
  isSidebar: true,
};

export const inventory: RouteItem = {
  name: "Inventory",
  id: ROUTES.ADMINSATELLITE.adminSatelliteInventory.key,
  path: ROUTES.ADMINSATELLITE.adminSatelliteInventory.key,
  component: InventoryTab,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
  icon: FaClipboardList,
  isSidebar: true,
};

export const viewForReceivingForm: RouteItem = {
  name: "View For Receiving Form",
  id: ROUTES.ADMINSATELLITE.viewForReceivingForm.key,
  path: ROUTES.ADMINSATELLITE.viewForReceivingForm.key,
  component: ViewFormReceiving,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
  isSidebar: false,
};

export const requestPads: RouteItem = {
  name: "Request Pads",
  id: ROUTES.ADMINSATELLITE.requestPads.key,
  path: ROUTES.ADMINSATELLITE.requestPads.key,
  component: RequestPads,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
  icon: TbMessageForward,
  isSidebar: true,
};

export const transmittalReturnedForm: RouteItem = {
  name: "Request Pads",
  id: ROUTES.ADMINSATELLITE.transmittalReturnedForm.key,
  path: ROUTES.ADMINSATELLITE.transmittalReturnedForm.key,
  component: TransmittalReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
};

export const viewPrTable: RouteItem = {
  name: "View PR Table",
  id: ROUTES.ADMINSATELLITE.viewPrTable.key,
  path: ROUTES.ADMINSATELLITE.viewPrTable.key,
  component: PRTable,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
  isSidebar: false,
};

export const issuePRForm: RouteItem = {
  name: "Issue PR Form",
  id: ROUTES.ADMINSATELLITE.issuePRForm.key,
  path: ROUTES.ADMINSATELLITE.issuePRForm.key,
  component: IssuePRForm,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
  isSidebar: false,
};

export const viewPR: RouteItem = {
  name: "View Issued/Cancelled PR",
  id: ROUTES.ADMINSATELLITE.viewPR.key,
  path: ROUTES.ADMINSATELLITE.viewPR.key,
  component: ViewPR,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
  isSidebar: false,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ADMINSATELLITE.notification.key,
  path: ROUTES.ADMINSATELLITE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
  icon: MdDashboard,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ADMINSATELLITE.profile.key,
  path: ROUTES.ADMINSATELLITE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
};

export const viewReleasedForm: RouteItem = {
  name: "View Returned Form",
  id: ROUTES.ADMINSATELLITE.viewReturnedForm.key,
  path: ROUTES.ADMINSATELLITE.viewReturnedForm.key,
  component: ViewReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.adminSatellite],
  isSidebar: false,
};

export const adminSatelliteRoutes = [
  overview,
  newForm,
  inventory,
  viewForReceivingForm,
  requestPads,
  transmittalReturnedForm,
  viewPrTable,
  issuePRForm,
  viewPR,
  notification,
  viewReleasedForm,
  profile,
];

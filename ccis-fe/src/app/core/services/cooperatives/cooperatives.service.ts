import httpClient from "@clients/httpClient";
import { IDefaultParams } from "@interface/common.interface";
import { TCooperativesPayload, TCooperativesWithIDAndIndexPayload } from "@state/types/cooperatives";

const apiResource = "cooperatives";
export const getCooperativessService = async (payload: IDefaultParams) => {
  let query = `${apiResource}?relations=cooperativeType|cooperativeAffiliations.affiliation|cooperativeOfficers.position|cooperativeCategory`;

  if (payload.filter) {
    query += `|cooperativeCategory&coopName[like]=${payload.filter}`;
  }
  if (payload.condition) {
    query += `&${payload.condition}`;
  }

  query += `&pageSize=${payload.pageSize ?? 10}&page=${payload.page ?? 1}`;

  return httpClient.get(query);
};

export const getCooperativesByIDService = async (id: number) => {
  return httpClient.get(`${apiResource}/${id}`);
};

export const postCooperativesService = async (payload: TCooperativesPayload) => {
  return httpClient.post(`${apiResource}`, payload);
};

export const putCooperativesService = async (payload: TCooperativesPayload) => {
  return httpClient.put(`${apiResource}/${payload.id}`, payload);
};

export const destroyCooperativesService = async (payload: TCooperativesWithIDAndIndexPayload) => {
  return httpClient.delete(`${apiResource}/${payload.id}`);
};

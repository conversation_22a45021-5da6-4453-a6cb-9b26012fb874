import { useWeeklyAccomplishmentReportActions } from "@state/reducer/weekly-accomplishment-report";
import { EmployeeReportsGrid } from "./components/EmployeeReportsTable";
import { TicketSummary } from "./components/TicketSummary";
import { WeeklyOverview } from "./components/WeeklyOverview";
import { useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { RootState } from "@state/store";

export default function ManagerWeeklyAccomplishmentDashboard() {
  const { getAllAccomplishmentReport } = useWeeklyAccomplishmentReportActions();
  const user = useSelector((state: any) => state?.auth?.user?.data);

  const accomplishmentResportsState = useSelector((state: RootState) => state?.weeklyAccomplishmentReport?.getAllAccomplishmentReport.data);
  // State for filters
  const [applicableMonth, setApplicableMonth] = useState<string>("");
  const [searchText, setSearchText] = useState<string>("");
  const [issueTypeFilter] = useState<string>("all");
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [employeeFilter, setEmployeeFilter] = useState<string>("");

  // Fetch data when dependencies change
  useEffect(() => {
    // Build condition string with all filters
    let condition = `applicableMonth[like]=${applicableMonth}&createdBy.departmentId[like]=${user?.departmentId || ""}`;

    // Add search filter if provided
    if (searchText) {
      condition += `&createdBy.firstname[like]|createdBy.lastname[like]=${searchText}`;
    }

    // Add issue type filter if needed (for future use)
    if (issueTypeFilter !== "all") {
      condition += `&accomplishmentTasks.issueType.issueTypeCode[like]=${issueTypeFilter}`;
    }

    if (employeeFilter) {
      condition += `&createdBy.id[eq]=${employeeFilter}`;
    }

    getAllAccomplishmentReport({
      params: {
        page,
        pageSize,
        sort: "id,desc",
        relations: "accomplishmentTasks.issueType|accomplishmentTasks.taskType|accomplishmentTasks.taskGroup|accomplishmentTasks.taskBlockers|createdBy",
        condition,
      },
    });
  }, [page, pageSize, applicableMonth, searchText, issueTypeFilter, user?.departmentId, employeeFilter]);

  const handleApplicableMonthChange = (month: string) => {
    setApplicableMonth(month);
    setPage(1); // Reset to first page when filter changes
  };

  const handleSearchChange = (search: string) => {
    setSearchText(search);
    setPage(1); // Reset to first page when search changes
  };
  // For Future Use
  // const handleIssueTypeFilterChange = (issueType: string) => {
  //   setIssueTypeFilter(issueType);
  //   setPage(1); // Reset to first page when filter changes
  // };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1); // Reset to first page when page size changes
  };

  const handleEmployeeFilterChange = (employeeId: string) => {
    setEmployeeFilter(employeeId);
    setPage(1); // Reset to first page when filter changes
  };

  return (
    <div className="min-h-screen">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          {/* @ts-ignore */}
          <WeeklyOverview accomplishmentReports={accomplishmentResportsState?.data || []} loading={accomplishmentResportsState?.loading} />
        </div>
        <div>
          <TicketSummary />
        </div>
      </div>
      <div className="mt-4">
        <EmployeeReportsGrid
          searchText={searchText}
          issueTypeFilter={issueTypeFilter}
          applicableMonth={applicableMonth}
          onApplicableMonthChange={handleApplicableMonthChange}
          // @ts-ignore
          loading={accomplishmentResportsState?.loading}
          // @ts-ignore
          accomplishmentReports={accomplishmentResportsState?.data}
          // If you want to handle search and filters from parent:
          // onIssueTypeFilterChange={handleIssueTypeFilterChange}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          page={page}
          pageSize={pageSize}
          handleSearch={handleSearchChange}
          employeeFilter={employeeFilter} 
          onEmployeeFilterChange={handleEmployeeFilterChange}
        />
      </div>
    </div>
  );
}

import { useState } from "react";
import { TableColumn } from "react-data-table-component";
import { User } from "lucide-react";
import { EmployeeReportModal } from "./modal/EmployeeReportsModal";
import Button from "@components/common/Button";
import Table from "@components/common/Table";
import Select from "@components/form/Select";
import { formatDate, issueTypes } from "../EmployeeReportForm";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import Loader from "@components/Loader";

interface EmployeeReportsGridProps {
  searchText?: string;
  issueTypeFilter?: string;
  applicableMonth?: string;
  onApplicableMonthChange?: (month: string) => void;
  loading?: boolean;
  accomplishmentReports?: any;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  page?: number;
  pageSize?: number;
  handleSearch?: (search: string) => void;
  employeeFilter?: string;
  onEmployeeFilterChange?: (employeeId: string) => void;
}

export function EmployeeReportsGrid({
  applicableMonth = "",
  onApplicableMonthChange,
  loading,
  accomplishmentReports,
  onPageChange,
  onPageSizeChange,
  handleSearch,
  employeeFilter,
  onEmployeeFilterChange,
}: EmployeeReportsGridProps) {
  const [selectedEmployee, setSelectedEmployee] = useState<any | null>(null);
  const accomplishmentResportsState = useSelector((state: RootState) => state?.weeklyAccomplishmentReport?.getAllAccomplishmentReport);
  const processEmployeeData = (report: any) => {
    const targetTasks = report.accomplishmentTasks.filter((task: any) => task.taskGroup.taskGroupCode === "Target");
    const actualTasks = report.accomplishmentTasks.filter((task: any) => task.taskGroup.taskGroupCode === "Actual");

    return {
      ...report,
      targetTasks,
      actualTasks,
      currentWorkCount: targetTasks.length,
      completedWorkCount: actualTasks.length,
    };
  };

  // Process and filter data - FIXED: Handle both array and object with data property
  const processedReports = accomplishmentReports
    ? Array.isArray(accomplishmentReports)
      ? accomplishmentReports.map(processEmployeeData)
      : accomplishmentReports.data
        ? accomplishmentReports.data.map(processEmployeeData)
        : []
    : [];

  // Get unique applicable months for the select dropdown - FIXED: Handle both data structures
  const getApplicableMonthOptions = () => {
    const reports = Array.isArray(accomplishmentReports) ? accomplishmentReports : accomplishmentReports?.data || [];

    if (!reports.length) return [];

    const uniqueMonths = Array.from(new Set(reports.map((report: any) => report.applicableMonth))) as string[];

    return uniqueMonths.map((month) => ({
      value: month,
      text: month,
    }));
  };

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<any>[] = [
    {
      name: "Employee",
      cell: (row) => (
        <div className="flex flex-col">
          <div className="font-semibold">
            {row.createdBy.firstname} {row.createdBy.lastname}
          </div>
          <div className="text-sm text-gray-500">{row.createdBy.email}</div>
        </div>
      ),
      width: "250px",
      ...commonSetting,
    },
    {
      name: "Period",
      cell: (row) => (
        <div className="text-sm">
          <div>
            {formatDate(row.periodFrom)} to {formatDate(row.periodTo)}
          </div>
          <div className="text-xs text-gray-400">Week {row.weekNumber}</div>
        </div>
      ),
      width: "150px",
      ...commonSetting,
    },
    {
      name: "Applicable Month",
      cell: (row) => <div className="text-sm font-medium">{row.applicableMonth}</div>,
      width: "150px",
      ...commonSetting,
    },
    {
      name: "Current Tasks",
      cell: (row) => <div className="text-center font-medium">{row.currentWorkCount}</div>,
      width: "120px",
      ...commonSetting,
    },
    {
      name: "Completed",
      cell: (row) => <div className="text-center font-medium">{row.completedWorkCount}</div>,
      width: "120px",
      ...commonSetting,
    },
    {
      name: "Total Hours",
      cell: (row) => <div className="text-center font-medium">{row.accomplishmentTasks.reduce((total: number, task: any) => total + task.numberOfHours, 0)}h</div>,
      width: "100px",
      ...commonSetting,
    },
    {
      name: "Issue Types",
      cell: (row) => (
        <div className="flex flex-wrap gap-1">
          {Array.from(new Set(row.targetTasks.map((task: any) => task.issueType.issueTypeCode))).map((issueTypeCode: any) => {
            // Find the matching issue type from issueTypes array
            const issueType = issueTypes.find((type) => type.code === issueTypeCode);
            const IconComponent = issueType?.icon; // Get the icon component
            return (
              <span
                key={issueTypeCode}
                className={`px-2 py-1 rounded text-xs flex items-center gap-1 whitespace-nowrap ${issueType?.color || "bg-gray-500"} text-white`}
                title={issueType?.name} // Optional: Add tooltip with issue type name
              >
                {IconComponent ? <IconComponent className="h-4 w-4" color={issueType?.textColor} /> : issueTypeCode} {issueType?.name}
              </span>
            );
          })}
        </div>
      ),
      width: "150px",
    },
    {
      name: "Latest Task",
      cell: (row) => (
        <div className="text-xs">
          {row.targetTasks.length > 0 && (
            <div>
              <strong>{row.targetTasks[0].referenceNumber}</strong>
              <br />
              {row.targetTasks[0].description.substring(0, 30)}...
            </div>
          )}
        </div>
      ),
      width: "200px",
    },
    {
      name: "Actions",
      cell: (row) => (
        <Button variant="primary" classNames="btn-sm" onClick={() => setSelectedEmployee(row)}>
          View Details
        </Button>
      ),
      width: "120px",
    },
  ];

  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (onApplicableMonthChange) {
      onApplicableMonthChange(e.target.value);
    }
  };

  // Calculate paginated data for client-side pagination
  // Note: For server-side pagination, remove this and pass full data to Table
  // const safePage = page ?? 1;
  // const safePageSize = pageSize ?? 10;
  // const startIndex = (safePage - 1) * safePageSize;
  // const endIndex = startIndex + safePageSize;
  // const paginatedData = filteredReports.slice(startIndex, endIndex);

  // Add this function after getApplicableMonthOptions
  const getEmployeeOptions = () => {
    const reports = Array.isArray(accomplishmentReports) ? accomplishmentReports : accomplishmentReports?.data || [];

    if (!reports.length) return [];

    const uniqueEmployees = Array.from(
      new Map(
        reports.map((report: any) => [
          report.createdBy.id,
          {
            id: report.createdBy.id,
            name: `${report.createdBy.firstname} ${report.createdBy.lastname}`,
            email: report.createdBy.email,
          },
        ])
      ).values()
    );

    return uniqueEmployees.map((employee: any) => ({
      value: employee.id.toString(),
      text: employee.name,
    }));
  };

  const handleEmployeeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (onEmployeeFilterChange) {
      onEmployeeFilterChange(e.target.value);
    }
  };

  return (
    <>
      <div className="flex flex-col mb-4">
        <div className="flex items-center justify-between mb-4">
          <label className="flex items-center gap-2">
            <User className="h-5 w-5 text-primary" />
            Employee Weekly Accomplishment Reports
          </label>
        </div>

        {/* Applicable Month Filter */}
        <div className="flex items-start gap-4 mb-4">
          <div className="w-64">
            <Select options={getApplicableMonthOptions()} value={applicableMonth} onChange={handleMonthChange} placeholder="Select Applicable Month" variant="primary" size="md" />
          </div>
          <div className="w-64">
            <Select options={getEmployeeOptions()} value={employeeFilter} onChange={handleEmployeeChange} placeholder="Select Employee" variant="primary" size="md" />
          </div>
        </div>
      </div>
      {/* @ts-ignore */}
      {accomplishmentResportsState?.loading ? (
        <Loader />
      ) : (
        <Table
          className="h-[600px]"
          columns={columns}
          data={processedReports} // Use filteredReports for server-side pagination
          loading={loading}
          searchable={true}
          multiSelect={false}
          pagination={true}
          // @ts-ignore
          paginationTotalRows={accomplishmentResportsState?.data?.meta.total} // Use total from API meta for server-side
          paginationServer={true} // Set to true for server-side pagination
          onPaginate={onPageChange}
          onChangeRowsPerPage={onPageSizeChange}
          onSearch={handleSearch}
          hideButton="invisible" // Hide the create button since we're using the select instead
        />
      )}

      <EmployeeReportModal employee={selectedEmployee} isOpen={!!selectedEmployee} onClose={() => setSelectedEmployee(null)} />
    </>
  );
}

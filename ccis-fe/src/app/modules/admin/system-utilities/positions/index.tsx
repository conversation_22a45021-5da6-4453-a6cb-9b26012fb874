import { FC, Fragment, useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { RootState } from "@state/store";
import { CiEdit, CiTrash } from "react-icons/ci";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import Modal from "@components/common/Modal";
import ActionButtons from "@components/common/ActionButtons";
import { confirmDelete, showSuccess } from "@helpers/prompt";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";
import { IUtilitiesPositions } from "@interface/utilities.interface";
import { Form, FormikProvider, useFormik } from "formik";
import TextField from "@components/form/TextField";
import Button from "@components/common/Button";
import { CreatePositionSchema, EditPositionSchema } from "@services/utilities-positions/utilities-positions.schema";

const PositionsTable: FC = () => {
  const [create, setCreate] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");
  // const [editData, setEditData] = useState<EditData>();

  const SuccessPostPositions = useSelector((state: RootState) => state.utilitiesPositions.postPosition?.success);
  const SuccessPutPositions = useSelector((state: RootState) => state.utilitiesPositions.putPosition?.success);
  const SuccessDestroyPositions = useSelector((state: RootState) => state.utilitiesPositions.destroyPosition?.success);

  const positions = useSelector((state: RootState) => state.utilitiesPositions.positions);
  const loading = useSelector((state: RootState) => state?.utilitiesPositions?.getPosition?.loading);
  const { getPosition, postPosition, putPosition, destroyPosition, setSelectedPosition } = usePositionsManagementActions();

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const actionEvents: IActions<IUtilitiesPositions>[] = [
    {
      name: "Edit",
      event: (row: IUtilitiesPositions, index: number) => {
        const data = {
          id: row.id,
          positionCode: row.positionCode,
          positionName: row.positionName,
          description: row.description,
        };
        formikEdit.setValues({
          id: data.id,
          positionCode: data.positionCode,
          positionName: data.positionName,
          description: data.description ?? "",
        });
        setSelectedPosition({ data: data, index: index });
        handleToggleEditModal();
      },
      icon: CiEdit,
      color: "primary",
    },
    {
      name: "Delete",
      event: (row: IUtilitiesPositions, index: number) => {
        const action = confirmDelete(row.positionName);
        action.then((value) => {
          if (value.isConfirmed) {
            destroyPosition({ id: row.id, index: index });
          }
        });
      },
      icon: CiTrash,
      color: "danger",
    },
  ];

  const columns: TableColumn<IUtilitiesPositions>[] = [
    {
      name: "Code",
      selector: (row) => row.positionCode,
      ...commonSetting,
    },
    {
      name: "Position Name",
      cell: (row) => row.positionName,
    },
    {
      name: "Description",
      cell: (row) => row.description,
    },

    {
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={actionEvents} />,
    },
  ];

  const handleToggleCreateModal = () => {
    setCreate((prev) => !prev);
  };

  const handleToggleEditModal = () => {
    setEdit((prev) => !prev);
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const handlePaginate = (value: any) => {
    console.log(value);
  };

  useEffect(() => {
    getPosition({ filter: searchText });
  }, [searchText]);

  const formik = useFormik({
    initialValues: {
      positionCode: "",
      positionName: "",
      description: "",
    },
    validationSchema: CreatePositionSchema,
    onSubmit: async (values, { resetForm }) => {
      postPosition(values);
      handleToggleCreateModal();
      resetForm();
    },
  });

  const formikEdit = useFormik({
    initialValues: {
      id: 0,
      positionCode: "",
      positionName: "",
      description: "",
    },
    validationSchema: EditPositionSchema,
    onSubmit: async (values) => {
      putPosition(values as any);
      handleToggleEditModal();
    },
  });
  useEffect(() => {
    if (SuccessPostPositions) {
      showSuccess("Success", "Position has been added");
    }
  }, [SuccessPostPositions]);

  useEffect(() => {
    if (SuccessPutPositions) {
      showSuccess("Success", "Position has been updated");
    }
  }, [SuccessPutPositions]);

  useEffect(() => {
    if (SuccessDestroyPositions) {
      showSuccess("Success", "Position has been deleted").then((result) => {
        if (result.isConfirmed) {
          window.location.reload();
        }
      });
    }
  }, [SuccessDestroyPositions]);

  return (
    <Fragment>
      <div className="text-xl font-semibold uppercase my-4">System Utilities / Positions</div>
      <Table
        className="h-[400px] "
        columns={columns}
        data={positions}
        createLabel="Create New Position"
        onCreate={handleToggleCreateModal}
        loading={loading}
        onSearch={handleSearch}
        onPaginate={handlePaginate}
        multiSelect={false}
        searchable
      />
      {create && (
        <Modal title="Create New Position" modalContainerClassName="max-w-3xl " titleClass="text-primary text-lg uppercase" isOpen={create} onClose={handleToggleCreateModal}>
          <>
            <FormikProvider value={formik}>
              <Form className="flex flex-col my-4 gap-4">
                <div>
                  {" "}
                  <label>Position Code</label>
                  <TextField
                    name="positionCode"
                    placeholder="Enter Position Code"
                    type="text"
                    className="bg-white"
                    error={formik.touched.positionCode && !!formik.errors.positionCode}
                    errorText={formik.errors.positionCode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.positionCode}
                    required
                  />
                </div>
                <div>
                  {" "}
                  <label>Position Name</label>
                  <TextField
                    name="positionName"
                    placeholder="Enter Position Name"
                    type="text"
                    className="bg-white"
                    error={formik.touched.positionName && !!formik.errors.positionName}
                    errorText={formik.errors.positionName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.positionName}
                    required
                  />
                </div>
                <div>
                  <label>Description</label>
                  <TextField
                    name="description"
                    type="text"
                    placeholder="Enter Description"
                    className="bg-white"
                    error={formik.touched.description && !!formik.errors.description}
                    errorText={formik.errors.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.description}
                  />
                </div>

                <Button type="submit" variant="primary" classNames="btn rounded-xl">
                  Save
                </Button>
              </Form>
            </FormikProvider>
          </>
        </Modal>
      )}
      {edit && (
        <Modal title="Edit Position" modalContainerClassName="max-w-3xl" titleClass="text-primary text-lg uppercase" isOpen={edit} onClose={handleToggleEditModal}>
          <>
            <FormikProvider value={formikEdit}>
              <Form className="flex flex-col my-4 gap-4">
                <div>
                  {" "}
                  <label>Position Code</label>
                  <TextField
                    name="positionCode"
                    placeholder="Enter Position Code"
                    type="text"
                    className="bg-white"
                    error={formikEdit.touched.positionCode && !!formikEdit.errors.positionCode}
                    errorText={formikEdit.errors.positionCode}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.positionCode}
                    required
                  />
                </div>
                <div>
                  {" "}
                  <label>Position Name</label>
                  <TextField
                    name="positionName"
                    placeholder="Enter Position Name"
                    type="text"
                    className="bg-white"
                    error={formikEdit.touched.positionName && !!formikEdit.errors.positionName}
                    errorText={formikEdit.errors.positionName}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.positionName}
                    required
                  />
                </div>
                <div>
                  <label>Description</label>
                  <TextField
                    name="description"
                    type="text"
                    placeholder="Enter Description"
                    className="bg-white"
                    error={formikEdit.touched.description && !!formikEdit.errors.description}
                    errorText={formikEdit.errors.description}
                    onChange={formikEdit.handleChange}
                    onBlur={formikEdit.handleBlur}
                    value={formikEdit.values.description}
                  />
                </div>

                <Button type="submit" variant="primary" classNames="btn rounded-xl">
                  Update
                </Button>
              </Form>
            </FormikProvider>
          </>
        </Modal>
      )}
    </Fragment>
  );
};

export default PositionsTable;

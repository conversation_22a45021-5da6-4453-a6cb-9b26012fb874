import { FC, useState, useEffect, useRef } from "react";
import Button from "@components/common/Button";
import { BiExport } from "react-icons/bi";
import { TbFileUpload } from "react-icons/tb";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
import FileDropzone from "@components/common/FileDropzone";
import { FaChevronRight, FaCloudArrowUp } from "react-icons/fa6";
import Typography from "@components/common/Typography";
import { Form, FormikProvider, useFormik } from "formik";
import { AxiosResponse } from "axios";
import { showSuccess } from "@helpers/prompt";
import { postProductProposalAgreement } from "@services/proposal/proposal.service";
import dayjs from "dayjs";
import { formatDate } from "@helpers/date";
import httpClient from "@clients/httpClient";
import { apiUrl } from "@services/variables";
import Loader from "@components/Loader";
import { capitalizeFirstLetterOnly, getTextStatusColor } from "@helpers/text";
import { agreementSchema } from "@services/product-proposal/product-proposal.schema";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { IUserRPermission } from "@interface/user.interface";
import { canCreateProductProposal } from "@helpers/product-proposal/product-proposal-permissions";
import AgreementSectionsContent from "./AgreementContent";
interface ProductProposalProps {
  data: any;
}

const PartnershipAgreement: FC<ProductProposalProps> = ({ data }) => {
  const [signedPartnershipAgreementModal, setSignedPartnershipAgreementModal] = useState<boolean>();

  const handleSignedPartnershipAgreementModal = () => {
    setSignedPartnershipAgreementModal((prev) => !prev);
  };

  const [files, setFiles] = useState<Array<File>>([]);
  const [imagePreview, setImagePreview] = useState<string | ArrayBuffer | null>(null);

  const [processing, setProcessing] = useState<boolean>(false);
  const [processModal, setProcessModal] = useState<boolean>(false);
  const toggleProcess = () => setProcessModal((prev) => !prev);
  const productGuidelines = data?.proposable?.productGuidelines ?? [];
  const guidelineRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [activeGuidelineIdx, setActiveGuidelineIdx] = useState<number | null>(null);

  const [activeSection, setActiveSection] = useState<string>("");
  const [pdfFile, setPdfFile] = useState<string>();

  //check permission
  const currentUser = useSelector((state: RootState) => state?.auth?.user?.data);
  const user = {
    ...currentUser,
    roles: currentUser?.roles ?? [],
  } as IUserRPermission;

  const canCreateProposal = canCreateProductProposal(user);

  //Sections in Column 2
  const sections = {
    appointment: { label: "Appointment", ref: useRef<HTMLDivElement>(null) },
    responsibility: {
      label: "Coop Partners Responsibility",
      ref: useRef<HTMLDivElement>(null),
    },
    climbs: {
      label: "Climbs Responsibilities",
      ref: useRef<HTMLDivElement>(null),
    },
    mutual: { label: "Mutual Obligations", ref: useRef<HTMLDivElement>(null) },
    relationship: {
      label: "Relationship of the Parties",
      ref: useRef<HTMLDivElement>(null),
    },
    confidentiality: {
      label: "Confidentiality Agreement",
      ref: useRef<HTMLDivElement>(null),
    },
    indemnification: {
      label: "Indemnification Agreement",
      ref: useRef<HTMLDivElement>(null),
    },
    termination: {
      label: "Termination of Contract",
      ref: useRef<HTMLDivElement>(null),
    },
    amendments: {
      label: "Amendments and venue of suit",
      ref: useRef<HTMLDivElement>(null),
    },
    separability: {
      label: "Separability clause",
      ref: useRef<HTMLDivElement>(null),
    },
  };

  const scrollToSection = (sectionRef: React.RefObject<HTMLDivElement>, sectionName: string) => {
    sectionRef.current?.scrollIntoView({ behavior: "smooth", block: "start" });
    setActiveSection(sectionName);
  };

  const scrollToGuideline = (idx: number) => {
    guidelineRefs.current[idx]?.scrollIntoView({ behavior: "smooth", block: "start" });
    setActiveGuidelineIdx(idx);
    setActiveSection("");
  };

  const handleExport = async () => {
    try {
      setProcessing(true);
      toggleProcess();

      const id = data?.id;
      const response: any = await httpClient.get(`${apiUrl}/product-proposals/${id}/export/agreement/pdf`, { responseType: "blob" });

      const pdfBlob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(pdfBlob);
      setPdfFile(url);
    } catch (error) {
      console.log(error);
    } finally {
      setProcessing(false);
    }
  };

  const handleFile = (acceptedFiles: Array<File>) => {
    setFiles(acceptedFiles);

    const file = new FileReader();
    file.onload = () => {
      setImagePreview(file.result);
    };

    file.readAsDataURL(acceptedFiles[0]);
  };

  useEffect(() => {
    if (files) {
      const fileArray = Array.from(files).map((file) => ({
        file: file,
        label: file.name,
        description: "Description here",
      }));

      formik.setFieldValue("attachments", fileArray);
    }
  }, [files]);

  const formik = useFormik({
    initialValues: {
      agreementSignedDate: "",
      agreementSignedRemarks: "",
      status: "SIGNED",
      attachments: [],
    },
    validationSchema: agreementSchema,
    onSubmit: async (values) => {
      try {
        const status: AxiosResponse = await postProductProposalAgreement(data?.id as number, values);

        if (status) {
          handleSignedPartnershipAgreementModal();
          showSuccess("Success", "Product Proposal Agreement has been updated!").then((result) => {
            if (result.isConfirmed) {
              window.location.reload();
            }
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
  });

  return (
    <div>
      {signedPartnershipAgreementModal && (
        <Modal isOpen={signedPartnershipAgreementModal} onClose={handleSignedPartnershipAgreementModal} modalContainerClassName="max-w-3xl">
          <FormikProvider value={formik}>
            <Form onSubmit={formik.handleSubmit}>
              <div className="w-full flex flex-col">
                <div className=" flex items-center justify-center text-center mb-10">
                  {" "}
                  <TbFileUpload size={50} className="text-primary" />
                </div>

                <div className="text-3xl text-center font-poppins-semibold mb-10"> Partnership Agreement</div>

                <div>
                  <label className="text-zinc-400">Please provide the coop signing date.</label>
                  <TextField
                    name="agreementSignedDate"
                    type="date"
                    value={formik.values.agreementSignedDate}
                    error={formik.touched.agreementSignedDate && !!formik.errors.agreementSignedDate}
                    errorText={formik.errors.agreementSignedDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    required
                  />
                </div>

                <div className="mt-4">
                  <label>Upload Signed Partnership Agreement</label>
                  <div className="border border-zinc-400 rounded-xl mt-2 w-full h-60 flex items-center justify-center">
                    <FileDropzone setFiles={handleFile} height={200}>
                      {files.length === 0 && (
                        <div className="flex flex-1 flex-col items-center">
                          <FaCloudArrowUp size={30} className="mb-4" />
                          <Typography>Click or drag and drop to upload your profile</Typography>
                          <Typography className="text-slate-400">PNG, JPG (Max 20MB)</Typography>
                        </div>
                      )}

                      {imagePreview && (
                        <div className="flex flex-1 flex-col items-center p-4 mb-4 rounded-md h-full w-full  ">
                          <img src={imagePreview as string} alt="Image Preview" className="w-full h-full object-contain" />
                        </div>
                      )}
                    </FileDropzone>
                  </div>
                  {formik.touched.attachments && formik.errors.attachments && <div className="text-red-500 text-sm mt-2">{formik.errors.attachments}</div>}
                </div>
                <div className="flex items-center justify-center gap-4 w-full mt-4">
                  <Button classNames="w-40 rounded-lg bg-zinc-500" onClick={handleSignedPartnershipAgreementModal}>
                    Cancel
                  </Button>
                  <Button type="submit" classNames="w-40 rounded-lg bg-info">
                    Submit
                  </Button>
                </div>
              </div>
            </Form>
          </FormikProvider>
        </Modal>
      )}
      {processModal && (
        <Modal isOpen={processModal} onClose={toggleProcess} showCloseButton={!processing} modalContainerClassName="!max-w-6xl">
          {processing && (
            <div className="flex flex-1 flex-col justify-center items-center">
              <Loader />
              <Typography>Processing...</Typography>
            </div>
          )}
          {!processing && (
            <div className="flex flex-1 flex-col justify-center items-center">
              <embed src={pdfFile} className="h-[500px]" width="100%" />
            </div>
          )}
        </Modal>
      )}

      <div className="p-4">
        <div className="w-full flex flex-col gap-4 mb-4">
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Product Proposed</div>
              <div className="w-2/3 text-black"> {data?.product?.name ?? ""} </div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Date Notarized</div>
              <div className="w-2/3 text-black">
                <div className="w-2/3 text-black">{formatDate(data?.proposalNotarization?.agreementNotarizationDate, "d MMMM yyyy") ?? "----"}</div>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Cooperative</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopName}</div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Notarized Status</div>
              <div className="w-2/3 text-black">
                {" "}
                <span className={`${getTextStatusColor(data.proposalNotarization?.agreementNotarizationStatus || "PENDING")}`}>
                  {capitalizeFirstLetterOnly((data.proposalNotarization?.agreementNotarizationStatus || "PENDING").replace(/_/g, " "))}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      {canCreateProposal ? (
        <div className="flex items-center justify-end mb-2">
          <Button onClick={handleExport} disabled={processing} type="button" variant="primary" outline classNames="text-xs flex items-center justify-center gap-2">
            <BiExport size={18} className="flex items-center justify-center" />
            {processing ? "Processing..." : "Export"}
          </Button>
        </div>
      ) : null}
      <div className="min-h-[50rem] w-full flex  justify-center gap-4  ">
        <div className="w-1/2 h-[50rem] border border-zinc-200 p-4 overflow-y-auto">
          {/* COL1 */}
          <div className="w-68 xl:flex flex-col text-start text-sm relative">
            <div className="w-full text-sm text-zinc-400 font-poppins-semibold py-4">Partnership Agreement</div>
            {Object.entries(sections).map(([key, { label, ref }]) => (
              <details key={key} className="group">
                <summary
                  onClick={() => scrollToSection(ref, key)}
                  className={`w-full items-center text-zinc-500 hover:bg-zinc-100 p-4 rounded-md cursor-pointer flex gap-2 ${activeSection === key ? "font-poppins-semibold text-black" : ""}`}
                >
                  <FaChevronRight />
                  {label.toUpperCase().replace("_", " ")}
                </summary>
              </details>
            ))}
            {productGuidelines.length > 0 && (
              <>
                <div className="w-full text-sm text-zinc-400 font-poppins-semibold py-4 mt-4">Product Guidelines</div>

                {productGuidelines.map((g: any, idx: number) => (
                  <details key={`pg-nav-${idx}`} className="group">
                    <summary
                      onClick={() => scrollToGuideline(idx)}
                      className={`w-full items-center text-zinc-500 hover:bg-zinc-100 p-4 rounded-md cursor-pointer flex gap-2 ${activeGuidelineIdx === idx ? "font-poppins-semibold text-black" : ""}`}
                      title={g.label}
                    >
                      <FaChevronRight />
                      {g.label}
                    </summary>
                  </details>
                ))}
              </>
            )}
          </div>
        </div>
        {/* COL2 */}
        <AgreementSectionsContent data={data} sections={sections} productGuidelines={productGuidelines} guidelineRefs={guidelineRefs} showCommission={false} />
        {/* COL3 */}
        <div className="w-1/3 flex flex-col items-center justify-center h-[50rem] border border-zinc-200 p-4">
          <div className="h-1/2 w-full">
            <div className="w-full text-xl  font-poppins-semibold my-4"> Signed Agreement</div>
            {data?.proposalAgreement === null && (
              <div>
                {canCreateProposal ? (
                  <>
                    <div className="p-2 text-zinc-400 text-xs rounded-md my-4">Please provide the coop signing date and upload the signed partnership agreement</div>
                    <Button
                      classNames={`w-full rounded-md my-2 text-sm ${data?.proposalApproval?.status === "FOR_REVIEW" ? "bg-slate-300" : "bg-info"}`}
                      isSubmitting={data?.proposalApproval?.status === "FOR_REVIEW"}
                      onClick={handleSignedPartnershipAgreementModal}
                    >
                      Upload
                    </Button>
                  </>
                ) : (
                  // Show this ONLY when there is no permission to create AND no upload exists
                  <div className="p-2 text-zinc-400 text-sm rounded-md my-4 text-center">No uploads found yet.</div>
                )}
              </div>
            )}
            {data?.proposalAgreement !== null && (
              <div>
                {" "}
                <div className="flex justify-between text-sm mb-4 border-b pb-4 border-zinc-300">
                  <div>Date Coop Signed</div>
                  <div>{data?.proposalAgreement?.agreementSignedDate !== null && <span>{dayjs(data?.proposalAgreement?.agreementSignedDate).format("MMMM DD, YYYY")}</span>}</div>
                </div>
                <div className="flex flex-col gap-4 mb-4 border-b pb-4 border-zinc-300 text-sm">
                  <div>Attachment</div>

                  <div className="underline text-accent hover:cursor-pointer">
                    <a
                      href={
                        (data?.proposalAgreement?.attachments?.[0] as any)?.filepath ? `${import.meta.env.VITE_AWS_S3_ENDPOINT}/${(data.proposalAgreement?.attachments?.[0] as any)?.filepath}` : "#"
                      }
                      target="_blank"
                      rel="noopener noreferrer" // This is important for security reasons
                    >
                      {data?.proposalAgreement?.attachments[0]?.label ? data?.proposalAgreement?.attachments[0]?.label : (formik.values?.attachments[0] as any)?.name}
                    </a>
                    <br />
                    {data?.proposalNotarization?.agreementNotarizationStatus === "NOTARIZED" && (
                      <a
                        href={
                          (data?.proposalNotarization?.attachments?.[0] as any)?.filepath
                            ? `${import.meta.env.VITE_AWS_S3_ENDPOINT}/${(data?.proposalNotarization?.attachments?.[0] as any)?.filepath}`
                            : "#"
                        }
                        target="_blank"
                        rel="noopener noreferrer" // This is important for security reasons
                      >
                        {data?.proposalNotarization?.attachments?.[0]?.label ? data?.proposalNotarization?.attachments?.[0]?.label : (formik.values?.attachments[0] as any)?.name}
                      </a>
                    )}
                  </div>
                </div>
                <div className="w-full">
                  {data?.proposalNotarization?.agreementNotarizationStatus !== "NOTARIZED" && canCreateProposal && (
                    <Button type="submit" classNames="w-full bg-info text-sm" onClick={handleSignedPartnershipAgreementModal}>
                      Replace
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="h-1/2 w-full">
            {" "}
            <div className="w-full text-xl font-poppins-semibold py-4 border-t border-zinc-200"> REMARKS</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PartnershipAgreement;

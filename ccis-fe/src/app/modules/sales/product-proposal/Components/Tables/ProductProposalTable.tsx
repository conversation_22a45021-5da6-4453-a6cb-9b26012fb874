import { FC, Fragment, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { RootState } from "@state/store";
import { CiEdit, CiTrash } from "react-icons/ci";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import ActionDropdown from "@components/common/ActionDropdown";
import { formatDate } from "@helpers/date";
import Typography from "@components/common/Typography";
import { FaArchive, FaHistory, FaSpinner, FaUndo } from "react-icons/fa";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { IProductProposal } from "@interface/product-proposal.interface";
import { capitalizeFirstLetterWords, getTextStatusColor } from "@helpers/text";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { ProposalStatus } from "@enums/proposal-status";
import { deleteProductProposalService } from "@services/product-proposal/product-proposal.service";
import { confirmArchive, confirmDelete, confirmRestore, showSuccess } from "@helpers/prompt";
import { toast } from "react-toastify";
import { GoVersions } from "react-icons/go";
import { useProductCategoryManagementActions } from "@state/reducer/utilities-product-category";
import { useProductTypesManagementActions } from "@state/reducer/utilities-product-type";
import { useTargetMarketsManagementActions } from "@state/reducer/utilities-target-market";
import { Status } from "@constants/global-constant-value";
import { ProposalTypes } from "@enums/enums";
import { UserRoles } from "@interface/routes.interface";
import { ProposableTypes } from "@enums/enums";
import { getPrecedingUserNameOfLatestApproved, getRejectedUserName } from "@helpers/sequentialSignatory";

interface ProductProposalTableProps {
  searchText?: string;
  dateFrom?: string;
  dateTo?: string;
  statusFilter?: string;
  typeFilter?: number;
  handleProductProposalLog?: (id: string) => void;
  showArchived?: boolean;
}

const ProductProposalTable: FC<ProductProposalTableProps> = ({ searchText, dateFrom, dateTo, statusFilter, typeFilter, handleProductProposalLog, showArchived = false }) => {
  const navigate = useNavigate();
  const [page, setPage] = useState<number>(1);
  const [rows, setRows] = useState<number>(10);
  const productProposals = useSelector((state: RootState) => state.productProposal.productProposals);
  const { getProductProposal, updateProductProposalStatus } = useProductProposalActions();
  const { getProductType } = useProductTypesManagementActions();
  const { getProductCategory } = useProductCategoryManagementActions();
  const { getTargetMarket } = useTargetMarketsManagementActions();
  const currentUser = useSelector((state: RootState) => state.auth.user.data);
  const responseData = useSelector((state: RootState) => state.productProposal.getProductProposal.data);
  const loading = useSelector((state: RootState) => state.productProposal.getProductProposal.loading);
  const [deleting, setDeleting] = useState<boolean>(false);
  const putStatusSuccess = useSelector((state: RootState) => state.productProposal.updateProductProposalStatus.success);

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const viewRouteByRole: Record<string, any> = {
    [UserRoles.admin]: ROUTES.ADMIN.viewProductProposal,
    [UserRoles.sales]: ROUTES.SALES.viewProductProposal,
    [UserRoles.rnd]: ROUTES.RESEARCHANDDEVELOPMENT.viewProductProposal,
    [UserRoles.actuary]: ROUTES.ACTUARY.viewProductProposal,
    [UserRoles.marketing]: ROUTES.MARKETING.viewProductProposalSales,
  };

  // prioritize which role wins when a user has multiple
  const VIEW_ROLE_PRIORITY: UserRoles[] = [UserRoles.admin, UserRoles.rnd, UserRoles.actuary, UserRoles.marketing, UserRoles.sales];

  const pickViewRouteForUser = () => {
    const roleNames: string[] = (currentUser?.roles ?? []).map((r: any) => (typeof r === "string" ? r : (r?.name ?? ""))).map((n: string) => n.toLowerCase());

    for (const role of VIEW_ROLE_PRIORITY) {
      const needle = role.toLowerCase();
      const hasRole = roleNames.some((n) => n === needle || n.includes(needle));
      if (hasRole && viewRouteByRole[role]) return viewRouteByRole[role];
    }
    // sensible fallback
    return ROUTES.SALES.viewProductProposal;
  };

  const getActionEvents = (productProposal: IProductProposal): IActions<IProductProposal>[] => {
    const actions: IActions<IProductProposal>[] = [
      ...([ProposalStatus.active, ProposalStatus.rejected].includes(productProposal.status as ProposalStatus)
        ? [
            {
              name: "View",
              event: (data: IProductProposal) => {
                let type = "standard"; // default type

                if (data.proposalType === "STANDARD" && data.proposableType === "PRODUCT_REVISION") {
                  type = "standard";
                } else if (data.proposalType === "CUSTOMIZED" && data.proposableType === "ACTUARY_EVALUATION_REPORT") {
                  type = "custom";
                }
                const route = pickViewRouteForUser();

                navigate(route.parse(data.id), { state: { proposal: data, type } });
              },
              icon: GoVersions,
              color: "primary",
            },
          ]
        : []),
      ...(productProposal.status === ProposalStatus.draft && !(productProposal.proposalType === ProposalTypes.CUSTOMIZED && productProposal.proposableType === ProposableTypes.AER)
        ? [
            {
              name: "Edit",
              event: (data: IProductProposal) => {
                const isAdmin = location.pathname.includes("admin");
                const route = isAdmin ? ROUTES.ADMIN.editProductProposal : ROUTES.SALES.editProductProposal;

                navigate(route.parse(data.id), {
                  state: { proposal: data },
                });
              },
              icon: CiEdit,
              color: "primary",
            },
          ]
        : []),
      ...(productProposal.status === ProposalStatus.draft && productProposal.proposalType === ProposalTypes.CUSTOMIZED && productProposal.proposableType === ProposableTypes.AER
        ? [
            {
              name: "Edit AER",
              event: (data: IProductProposal) => {
                const quotationId = (data?.proposable as any)?.quotationId;
                if (!quotationId) {
                  toast.error("Quotation ID is missing");
                  return;
                }

                sessionStorage.setItem("cameFromStep3WithAER", "true"); // auto-open AER

                navigate(ROUTES.SALES.editProductProposal.parse(quotationId.toString()), {
                  state: {
                    proposal: data?.quotation,
                    proposalId: data.id,
                    cameFromStep3WithAER: true,
                  },
                });
              },
              icon: CiEdit,
              color: "primary",
            },
          ]
        : []),

      ...(productProposal.status === ProposalStatus.draft || productProposal.status === ProposalStatus.rejected
        ? [
            {
              name: "Delete",
              event: (data: IProductProposal) => {
                handleDelete(data);
              },
              icon: CiTrash,
              color: "danger",
            },
          ]
        : []),
      ...(productProposal.status === ProposalStatus.draft || productProposal.status === ProposalStatus.rejected
        ? [
            {
              name: "Archive",
              event: (data: IProductProposal) => {
                handleArchive(data);
              },
              icon: FaArchive,
              color: "accent",
            },
          ]
        : []),
      ...(productProposal.status === ProposalStatus.archived
        ? [
            {
              name: "Restore",
              event: (data: IProductProposal) => {
                handleRestore(data);
              },
              icon: FaUndo,
              color: "success",
            },
          ]
        : []),
      {
        name: "Logs",
        event: (data: IProductProposal) => {
          handleProductProposalLog && handleProductProposalLog(data.id);
        },
        icon: FaHistory,
        color: "primary",
      },
    ];

    return actions;
  };
  const columns: TableColumn<IProductProposal>[] = [
    {
      name: "Product Name",
      cell: (row) => row?.product?.name ?? "Not Set",
      width: "250px",
      ...commonSetting,
    },
    {
      name: "Cooperative",
      cell: (row) => row.cooperative?.coopName ?? "Not Set",
      width: "300px",
      ...commonSetting,
    },
    {
      name: "Product Type",
      cell: (row) => row?.product?.productType?.productType ?? "",
      ...commonSetting,
    },
    {
      name: "Proposal Type",
      cell: (row) => row?.proposalType ?? "",
      ...commonSetting,
    },
    {
      name: "Sub Type",
      cell: (row) => {
        const subType =
          row?.proposableType === ProposableTypes.AER
            ? "AER"
            : row?.proposableType === ProposableTypes.PRODUCT_REVISION
              ? row?.proposalType === ProposalTypes.CUSTOMIZED
                ? "Provision"
                : "Product Revision"
              : (row?.proposableType ?? "");
        return <span className="text-primary">{subType}</span>;
      },
      width: "100px",
    },
    {
      name: "Creation Date",
      cell: (row) => formatDate(row?.createdAt, "d MMMM yyyy"),
      width: "200px",
      ...commonSetting,
    },
    {
      name: "Tracking",
      width: "250px",
      cell: (row) => <div className="p-2 rounded-md flex items-center justify-center w-full h-full">{handleTracking(row)}</div>,
      center: true,
      ...commonSetting,
    },

    {
      name: "Status",
      cell: (row) => (
        <Typography size="xs" className={`${getTextStatusColor(row.status)}`}>
          {capitalizeFirstLetterWords(row.status, "_")}
        </Typography>
      ),
    },
    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => {
        return (
          <div className="flex flex-1 flex-row justify-center items-center gap-x-2">
            {deleting && <FaSpinner className="animate-spin" />}
            {!deleting && <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />}
          </div>
        );
      },
    },
  ];
  const fetchProposals = () => {
    getProductProposal({
      filter: searchText,
      dateFrom: dateFrom,
      dateTo: dateTo,
      statusFilter: showArchived ? undefined : statusFilter,
      productTypeFilter: typeFilter,
      showArchived: showArchived,

      page,
      pageSize: rows,
      user: currentUser?.id,
    });
  };

  useEffect(() => {
    if (putStatusSuccess) {
      showSuccess("Success", "Product proposal status updated successfully");
      fetchProposals();
    }
  }, [putStatusSuccess]);

  const handleDelete = async (proposal: IProductProposal) => {
    try {
      const confirm = await confirmDelete(`proposal for ${proposal?.cooperative?.coopName ?? "Not Set"}`);
      if (confirm.isConfirmed) {
        setDeleting(true);
        const { data } = await deleteProductProposalService(proposal.id);
        if (data) {
          showSuccess("Success", "Product proposal has been deleted successfully");
          fetchProposals();
        }
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setDeleting(false);
    }
  };

  const handleArchive = async (proposal: IProductProposal) => {
    try {
      const confirm = await confirmArchive(`proposal for ${proposal?.cooperative?.coopName ?? "Not Set"}`);
      if (confirm.isConfirmed) {
        updateProductProposalStatus({
          id: proposal.id.toString(),
          status: ProposalStatus.archived,
        });
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message || "Failed to archive product proposal");
    }
  };

  const handleRestore = async (proposal: IProductProposal) => {
    try {
      const confirm = await confirmRestore(`proposal for ${proposal?.cooperative?.coopName ?? "Not Set"}`);
      if (confirm.isConfirmed) {
        updateProductProposalStatus({
          id: proposal.id.toString(),
          status: ProposalStatus.draft,
        });
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message || "Failed to restore product proposal");
    }
  };

  const handlePaginate = (pagination: number) => {
    setPage(pagination);
  };

  const handleRowsChange = (rowsPerPage: number, pagination: number) => {
    setRows(rowsPerPage);
    setPage(pagination);
  };
  const handleTracking = (item: any) => {
    let text = "";
    let bg = " text-primary"; // default

    if (item?.proposalApproval?.status === Status.for_review) {
      text = "Sales For Coop Approval";
    } else if (item?.proposalApproval?.status === Status.approved && item?.requirementable?.status === Status.pending) {
      text = "Sales Upload Requirements";
    } else if (item?.proposalApproval?.status === Status.rejected && item?.requirementable?.status === Status.pending) {
      text = "Rejected By Coop";
      // bg = "bg-red-100 text-red-800";
    } else if (item?.requirementable?.status === Status.for_approval && item?.proposalApproval?.status === Status.approved) {
      text = "Marketing Requirements Approval";
    } else if (item?.requirementable?.status === Status.for_revision && item?.proposalApproval?.status === Status.approved) {
      text = "Marketing Rejected The Requirements";
      bg = "text-red-500 ";
    } else if (item?.requirementable?.status === Status.valid && item?.proposalApproval?.status === Status.approved && !item?.commissionStructure) {
      text = "Sales Setup Commission Structure";
    } else if (item?.commissionStructure?.status === Status.for_approval && item?.requirementable?.status === Status.valid && !item?.commissionStructure?.approval) {
      text = "Marketing Will Set Approver";
    } else if (item?.commissionStructure?.status === Status.for_approval && item?.requirementable?.status === Status.valid && item?.commissionStructure?.approval?.status === Status.pending) {
      text = `Waiting for Approver ${getPrecedingUserNameOfLatestApproved(item?.commissionStructure?.approval?.signatories)}`;
    } else if (item?.commissionStructure?.status === Status.rejected && item?.requirementable?.status === Status.valid && item?.commissionStructure?.approval?.status === Status.rejected) {
      text = `Rejected by Approver ${getRejectedUserName(item?.commissionStructure?.approval?.signatories)}`;
      bg = "text-red-500";
    } else if (item?.commissionStructure?.status === Status.pending && item?.requirementable?.status === Status.valid && !item?.commissionStructure?.approva) {
      text = "Sales - Commission mark as Draft";
    } else if (
      item?.commissionStructure?.status === Status.approved &&
      item?.requirementable?.status === Status.valid &&
      item?.commissionStructure?.approval?.status === Status.approved &&
      item?.agreementStatus !== Status.notarized
    ) {
      text = "Waiting for Partnership Agreement Setup";
    } else if (
      item?.commissionStructure?.status === Status.approved &&
      item?.requirementable?.status === Status.valid &&
      item?.commissionStructure?.approval?.status === Status.approved &&
      item?.agreementStatus === Status.notarized
    ) {
      text = "Completed";
      bg = "text-green-800";
    } else {
      text = "On-going Creation";
      bg = "text-black";
    }

    return <div className={`flex items-center justify-center rounded-md font-semibold text-center w-full h-full text-[14px]  ${bg}`}>{text}</div>;
  };
  useEffect(() => {
    fetchProposals();
  }, [searchText, dateFrom, dateTo, statusFilter, typeFilter, page, rows]);

  useEffect(() => {
    getProductType({ filter: "" });
    getProductCategory({ filter: "" });
    getTargetMarket({ filter: "" });
  }, []);

  return (
    <Fragment>
      <div className="flex mt-4">
        <Table
          className="h-[600px]"
          columns={columns}
          data={productProposals}
          loading={loading}
          searchable={false}
          multiSelect={false}
          paginationTotalRows={responseData?.meta?.total}
          paginationServer={true}
          onPaginate={handlePaginate}
          onChangeRowsPerPage={handleRowsChange}
        />
      </div>
    </Fragment>
  );
};

export default ProductProposalTable;

import Typography from "@components/common/Typography";
import { RootState } from "@state/store";
import { useSelector } from "react-redux";
import { FC, Fragment, useEffect, useMemo, useState } from "react";
import { IGuidelineContent, IGuidelineContentTable } from "@interface/guidelines.interface";
import { FaPen, FaRedo } from "react-icons/fa";
import LoadingButton from "@components/common/LoadingButton";
import Tooltip from "@components/common/Tooltip";
import { ProposalStatus } from "@enums/proposal-status";
import { toast } from "react-toastify";
import { useProductActions } from "@state/reducer/products";
import { getRevisionDetailsService } from "@services/products/products.service";

type TProps = {
  setStep: () => void;
  handleChange: () => void;
  handleSaveAsDraft: () => void;
  toggleManagementFee: () => void;
  toggleIsEditProvisions: () => void;
  submitting?: boolean;
  setSubmitting: (value: boolean) => void;
};

const SelectedProductDetails: FC<TProps> = ({ setStep, handleChange, handleSaveAsDraft, toggleManagementFee, toggleIsEditProvisions, submitting, setSubmitting }) => {
  const proposedProduct = useSelector((state: RootState) => state.productProposal.proposedProduct);
  const revisionDetails = useSelector((state: RootState) => state.products.revisionDetails);
  const managementPercentFee = useSelector((state: RootState) => state.productProposal.managementPercentFee);
  const customType = useSelector((state: RootState) => state.productProposal.customType);
  const isEditedGuidelines = useSelector((state: RootState) => state.productProposal.isEditedGuidelines);
  const { setRevisionDetails } = useProductActions();
  const [isLoadingFullDetails, setIsLoadingFullDetails] = useState(false);

  // revisionDetails first, fallback to proposedProduct.productRevision for edit mode
  const currentRevisionDetails = useMemo(() => {
    return revisionDetails || proposedProduct?.productRevision;
  }, [revisionDetails, proposedProduct?.productRevision]);

  const standard = currentRevisionDetails?.commission?.commissionDetails?.filter((rowValue) => rowValue.commissionAgeType?.name?.toLowerCase() !== "standard") ?? [];

  const handleSave = async () => {
    try {
      setSubmitting(true);
      if (customType && !isEditedGuidelines) {
        toast.info("Product guidelines remain uncustomized");
        return;
      }
      if (handleSaveAsDraft) {
        await handleSaveAsDraft();
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setSubmitting(false);
    }
  };

  const handleContinue = () => {
    // handle customize flow
    if (customType && !isEditedGuidelines) {
      toast.info("Product guidelines remain uncustomized");
      return;
    }

    setStep();
  };

  // Early return if no revision details available
  if (!currentRevisionDetails) {
    return (
      <div className="flex flex-1 flex-col p-2 space-y-4">
        <Typography className="text-center text-gray-500">No product details available</Typography>
      </div>
    );
  }
  useEffect(() => {
    const needsFullDetails = revisionDetails && revisionDetails.id && revisionDetails.productId && (!revisionDetails.productGuidelines || !revisionDetails.commission);

    if (needsFullDetails && !isLoadingFullDetails) {
      const fetchFullDetails = async () => {
        try {
          setIsLoadingFullDetails(true);

          const { data } = await getRevisionDetailsService({
            productid: revisionDetails.productId?.toString() ?? "",
            revisionid: revisionDetails.id?.toString() ?? "",
          });

          if (data) {
            setRevisionDetails(data);
          }
        } catch (error) {
          toast.error("Failed to load complete product details");
        } finally {
          setIsLoadingFullDetails(false);
        }
      };

      fetchFullDetails();
    }
  }, [revisionDetails?.id, revisionDetails?.productGuidelines, revisionDetails?.commission]);

  if (isLoadingFullDetails) {
    return (
      <div className="flex flex-1 flex-col items-center justify-center p-8">
        <div className="loading loading-spinner loading-lg"></div>
        <Typography className="mt-4">Loading product details...</Typography>
      </div>
    );
  }

  // Early return if no revision details available
  if (!currentRevisionDetails) {
    return (
      <div className="flex flex-1 flex-col p-2 space-y-4">
        <Typography className="text-center text-gray-500">No product details available</Typography>
      </div>
    );
  }
  return (
    <div className="flex flex-1 flex-col p-2 space-y-4">
      <Fragment>
        {proposedProduct?.status !== ProposalStatus.approved && (
          <Fragment>
            <div className="flex flex-1 flex-col justify-between mt-2">
              <Typography className="text-accent !text-md">SELECTED PRODUCT</Typography>
              <Typography className="!text-sm">Review the selected product guidelines to ensure accurate presentation</Typography>
            </div>
            <div className="divider"></div>
          </Fragment>
        )}

        <div className="flex flex-1 flex-row justify-between mt-2">
          <Typography className="font-poppins-semibold" size="xl">
            {currentRevisionDetails.product?.name ?? "No Product Name Set"}
          </Typography>
          {proposedProduct?.status !== ProposalStatus.approved && (
            <button className="btn bg-ghost text-accent btn-sm px-6" onClick={handleChange}>
              <FaRedo />
              Change Product
            </button>
          )}
        </div>
        <div className={`flex flex-1 flex-col  overflow-y-auto max-h-[400px] min-h-[${proposedProduct?.status !== ProposalStatus.approved ? "400px" : "320px"}]`}>
          <div className="flex flex-1 flex-col justify-start">
            <Typography className="font-poppins-semibold">Product Description</Typography>
            <Typography className="ml-4 mt-4">{currentRevisionDetails.product?.description?.replace(/<[^>]*>/g, "") ?? "No Product Description Set"}</Typography>
          </div>
          <div className="flex flex-1 flex-col justify-start mt-10">
            {currentRevisionDetails?.productGuidelines?.map((value, gIndex) => {
              return (
                <div key={`guideline-${gIndex}`} className="flex flex-1 flex-col mb-10">
                  <Typography className="text-[18px] mb-1 font-poppins-semibold">{value.label}</Typography>
                  {value.productGuideline.map((pgValue, pgIndex) => {
                    let listValue;
                    let tableValue;
                    if (pgValue.type === "list") {
                      listValue = pgValue.value as IGuidelineContent[];
                    }

                    if (pgValue.type === "table") {
                      tableValue = pgValue.value as IGuidelineContentTable;
                    }

                    return (
                      <div key={`pg-${pgIndex}`}>
                        {pgValue.type === "textfield" && (
                          <Fragment>
                            <Typography className="ml-4 mt-4 text-justify">{pgValue.value as string}</Typography>
                          </Fragment>
                        )}
                        {pgValue.type === "list" && (
                          <Fragment>
                            <Typography className="ml-4 mt-4 text-justify">{pgValue.label}</Typography>
                            <ul className="list-disc ml-12">
                              {listValue &&
                                listValue.map((listValue, listIndex) => {
                                  return (
                                    <li key={`listItem-${listIndex}`} className="mt-4">
                                      <Typography className="text-justify">{listValue.value as string}</Typography>
                                    </li>
                                  );
                                })}
                            </ul>
                          </Fragment>
                        )}
                        {pgValue.type === "texteditor" && (
                          <Fragment>
                            <div className="text-justify text-wrap px-8">
                              <div
                                className="ml-2 mt-2"
                                dangerouslySetInnerHTML={{
                                  __html: pgValue.value ?? "",
                                }}
                              ></div>
                            </div>
                          </Fragment>
                        )}
                        {pgValue.type === "table" && (
                          <Fragment>
                            <div className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                              <table className="table border-[1px]">
                                <thead className="table-header-group">
                                  <tr>
                                    {tableValue?.columns?.map((cValue, cIndex) => {
                                      return (
                                        <td key={`col-${cIndex}`} className="table-cell border-[1px]">
                                          <Typography className="font-semibold text-xs">{cValue.value as string}</Typography>
                                        </td>
                                      );
                                    })}
                                  </tr>
                                </thead>
                                <tbody>
                                  {tableValue?.rows?.map((rValue, rIndex) => {
                                    return (
                                      <tr key={`row-${rIndex}`}>
                                        {rValue.map((cell, cellIndex) => {
                                          return (
                                            <td className="border-[1px] text-xs" key={`cell-${cellIndex}`}>
                                              <Typography>{cell.value as string}</Typography>
                                            </td>
                                          );
                                        })}
                                      </tr>
                                    );
                                  })}
                                </tbody>
                              </table>
                            </div>
                          </Fragment>
                        )}
                      </div>
                    );
                  })}
                </div>
              );
            })}
          </div>
          <div className="flex flex-1 flex-col justify-start">
            {currentRevisionDetails?.commission && (
              <Fragment>
                <Typography className="font-poppins-semibold" size="md">
                  Commission Structure
                </Typography>
                <Fragment>
                  <Typography size="sm" className="ml-4 mt-4">
                    {parseFloat(currentRevisionDetails?.commission?.maximumDisposableRate ?? "").toFixed(2)}% Maximum Disposable Commission - Standard Rate
                  </Typography>
                  <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll">
                    <table className="table overflow-scroll">
                      <thead>
                        <tr>
                          <td className="table-cell border-[1px] text-center text-xs">Type</td>
                          <td className="table-cell border-[1px] text-center text-xs">Age Type</td>
                          {standard.length > 0 && (
                            <Fragment>
                              <td className="table-cell border-[1px] text-center text-xs">Age From</td>
                              <td className="table-cell border-[1px] text-center text-xs">Age To</td>
                            </Fragment>
                          )}
                          <td className="table-cell border-[1px] text-center text-xs">Rate</td>
                        </tr>
                      </thead>
                      <tbody>
                        {currentRevisionDetails?.commission.commissionDetails?.map((rowValue, rowIndex) => {
                          return (
                            <tr key={`commissionDetailsRow-${rowIndex}`}>
                              <td className="table-cell border-[1px] text-xs">{rowValue?.commissionType?.commissionName}</td>
                              <td className="table-cell border-[1px] text-xs">{rowValue?.commissionAgeType?.name}</td>
                              {standard.length > 0 && (
                                <Fragment>
                                  <td className="table-cell border-[1px] text-center text-xs">{rowValue.ageFrom}</td>
                                  <td className="table-cell border-[1px] text-center text-xs">{rowValue.ageTo}</td>
                                </Fragment>
                              )}
                              <td className="table-cell border-[1px] text-center text-xs">{rowValue.rate ? parseFloat(rowValue.rate.toString()).toFixed(0) : ""}%</td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </Fragment>
              </Fragment>
            )}
          </div>
          <div className="flex flex-1 flex-col justify-start">
            <Typography className="font-poppins-semibold mt-4">Added Benefits for the Cooperative</Typography>
            <div className="flex flex-1 flex-row items-center">
              <Typography className="mt-4 ml-4">Management Fee - {managementPercentFee}%</Typography>
              {proposedProduct?.status !== ProposalStatus.approved && (
                <Tooltip text="Edit Management Fee">
                  <button className="btn btn-sm rounded-full mt-4 ml-4" onClick={toggleManagementFee}>
                    <FaPen size={15} />
                  </button>
                </Tooltip>
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-1 flex-row justify-between mt-4">
          <div className="flex flex-1">
            {customType && (
              <LoadingButton isLoading={submitting} onClick={toggleIsEditProvisions} className="btn rounded-lg hover:bg-slate-200 bg-white !text-primary !w-44 mr-4 mt-4" type="submit">
                Edit Provisions
              </LoadingButton>
            )}
          </div>
          <div className="flex flex-1 flex-row justify-end">
            {proposedProduct?.status !== ProposalStatus.approved && (
              <LoadingButton isLoading={submitting} onClick={handleSave} className="btn rounded-lg hover:bg-slate-200 bg-white !text-primary !w-32 mr-4 mt-4" type="submit">
                Save as draft
              </LoadingButton>
            )}
            <LoadingButton isLoading={submitting} onClick={handleContinue} className="btn rounded-lg bg-accent text-white !w-32 mr-4 mt-4" type="button">
              Continue
            </LoadingButton>
          </div>
        </div>
      </Fragment>
    </div>
  );
};

export default SelectedProductDetails;

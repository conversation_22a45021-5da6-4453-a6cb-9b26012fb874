import Typography from "@components/common/Typography";
import CheckBox from "@components/form/CheckBox";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { formatSelectOptions } from "@helpers/array";
import { ICooperativeOfficer } from "@interface/product-proposal.interface";
import { CoopOfficerSchema } from "@services/product-proposal/product-proposal.schema";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";
import { RootState } from "@state/store";
import { Form, FormikProvider, useFormik } from "formik";
import { FC, useEffect } from "react";
import { useSelector } from "react-redux";
import testData from "@constants/json-file/data.json";
import { useSelectOptions } from "@hooks/useSelectOptions";
import TextArea from "@components/form/TextArea";

type TProps = {
  selectedCoopOfficer?: ICooperativeOfficer & { index: number };
  resetForm: () => void;
  toggleModal: () => void;
  addCoopOfficer: (values: ICooperativeOfficer) => void;
  editCoopOfficer: (data: ICooperativeOfficer & { index: number }) => void;
};

const CoopOfficerForm: FC<TProps> = ({ selectedCoopOfficer, toggleModal, addCoopOfficer, editCoopOfficer, resetForm }) => {
  const { getPosition } = usePositionsManagementActions();
  const positions = useSelector((state: RootState) => state.utilitiesPositions.positions);
  const positionOptions = formatSelectOptions(positions, "positionName");

  const getPositionName = (id: string) => {
    return positionOptions.find((position) => {
      return position.value == id;
    })?.text;
  };

  const getPositionDetails = (id: string) => {
    return positions.find((position) => {
      return position.id.toString() == id;
    });
  };

  const formikOfficer = useFormik({
    initialValues: selectedCoopOfficer ?? {
      title: "",
      firstName: "",
      middleName: "",
      lastName: "",
      generation: "",
      gender: "",
      emailAddress: "",
      contactNumber: "",
      effectivityDate: "",
      status: "",
      positionId: "",
      positionName: "",
      signatory: false,
      maritalStatus: "",
      address: "",
    },
    validationSchema: CoopOfficerSchema,
    onSubmit: (values) => {
      if (selectedCoopOfficer) {
        const updatedValue = { ...values, index: selectedCoopOfficer.index, positionName: getPositionName(values.positionId ?? ""), position: getPositionDetails(values.positionId ?? "") };
        editCoopOfficer && editCoopOfficer(updatedValue);
      } else {
        const updatedValue = { ...values, positionName: getPositionName(values.positionId ?? ""), position: getPositionDetails(values.positionId ?? "") };
        addCoopOfficer && addCoopOfficer(updatedValue);
      }

      toggleModal && toggleModal();
    },
  });
  useEffect(() => {
    getPosition({ filter: "" });

    return () => {
      resetForm();
    };
  }, []);
  const maritalStatusOptions = useSelectOptions({
    data: testData.maritalStatus,
    firstOptionText: "Select Marital Status",
    valueKey: "value",
    textKey: ["text"],
  });
  return (
    <FormikProvider value={formikOfficer}>
      <Form>
        <div className="flex flex-1 flex-col space-y-2">
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Title</Typography>
            <div className="flex flex-1 flex-col">
              <TextField
                name="title"
                value={formikOfficer.values.title}
                onChange={formikOfficer.handleChange}
                error={!!formikOfficer.errors.title && formikOfficer.touched.title}
                errorText={formikOfficer.errors.title}
                size="sm"
                placeholder="Enter Title"
                className="mt-2"
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">First Name</Typography>
            <div className="flex flex-1 flex-col">
              <TextField
                name="firstName"
                value={formikOfficer.values.firstName}
                onChange={formikOfficer.handleChange}
                error={!!formikOfficer.errors.firstName && formikOfficer.touched.firstName}
                errorText={formikOfficer.errors.firstName}
                size="sm"
                placeholder="Enter first name"
                className="mt-2"
                required
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Middle Name</Typography>
            <div className="flex flex-1 flex-col">
              <TextField
                name="middleName"
                value={formikOfficer.values.middleName}
                onChange={formikOfficer.handleChange}
                error={!!formikOfficer.errors.middleName && formikOfficer.touched.middleName}
                errorText={formikOfficer.errors.middleName}
                size="sm"
                placeholder="Enter middle name"
                className="mt-2"
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Last Name</Typography>
            <div className="flex flex-1 flex-col">
              <TextField
                name="lastName"
                value={formikOfficer.values.lastName}
                onChange={formikOfficer.handleChange}
                error={!!formikOfficer.errors.lastName && formikOfficer.touched.lastName}
                errorText={formikOfficer.errors.lastName}
                size="sm"
                placeholder="Enter last name"
                className="mt-2"
                required
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Suffix</Typography>
            <div className="flex flex-1 flex-col">
              <TextField
                type="text"
                name="generation"
                value={formikOfficer.values.generation}
                onChange={formikOfficer.handleChange}
                error={!!formikOfficer.errors.generation && formikOfficer.touched.generation}
                errorText={formikOfficer.errors.generation}
                size="sm"
                placeholder="Enter Suffix"
                className="mt-2"
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Email Address</Typography>
            <div className="flex flex-1 flex-col">
              <TextField
                type="email"
                name="emailAddress"
                value={formikOfficer.values.emailAddress}
                onChange={formikOfficer.handleChange}
                error={!!formikOfficer.errors.emailAddress && formikOfficer.touched.emailAddress}
                errorText={formikOfficer.errors.emailAddress}
                size="sm"
                placeholder="Email Address"
                className="mt-2"
                required
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Effectivity Date</Typography>
            <div className="flex flex-1 flex-col">
              <TextField
                type="date"
                name="effectivityDate"
                value={formikOfficer.values.effectivityDate}
                onChange={formikOfficer.handleChange}
                error={!!formikOfficer.errors.effectivityDate && formikOfficer.touched.effectivityDate}
                errorText={formikOfficer.errors.effectivityDate}
                size="sm"
                placeholder="Appointment Date"
                className="mt-2"
                required
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Contact Number</Typography>
            <div className="flex flex-1 flex-col">
              <TextField
                type="number"
                name="contactNumber"
                value={formikOfficer.values.contactNumber}
                onChange={(e) => {
                  const value = e.target.value.toString();
                  formikOfficer.setFieldValue("contactNumber", value);
                }}
                error={!!formikOfficer.errors.contactNumber && formikOfficer.touched.contactNumber}
                errorText={formikOfficer.errors.contactNumber}
                size="sm"
                placeholder="Contact Number"
                className="mt-2"
                required
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Gender</Typography>
            <div className="flex flex-1 flex-col">
              <Select
                value={formikOfficer.values.gender}
                options={[
                  { text: "Male", value: "male" },
                  { text: "Female", value: "female" },
                ]}
                onChange={formikOfficer.handleChange}
                error={!!formikOfficer.errors.gender && formikOfficer.touched.gender}
                errorText={formikOfficer.errors.gender}
                variant="primary"
                name="gender"
                className="mt-2"
                size="sm"
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Position</Typography>
            <div className="flex flex-1 flex-col">
              <Select
                value={formikOfficer.values.positionId}
                options={positionOptions}
                onChange={formikOfficer.handleChange}
                error={!!formikOfficer.errors.positionId && formikOfficer.touched.positionId}
                errorText={formikOfficer.errors.positionId}
                name="positionId"
                variant="primary"
                className="mt-2 "
                size="sm"
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Status</Typography>
            <div className="flex flex-1 flex-col">
              <Select
                value={formikOfficer.values.status}
                options={[
                  { text: "Active", value: "active" },
                  { text: "Inactive", value: "inactive" },
                ]}
                onChange={formikOfficer.handleChange}
                error={!!formikOfficer.errors.status && formikOfficer.touched.status}
                errorText={formikOfficer.errors.status}
                name="status"
                variant="primary"
                className="mt-2 "
                size="sm"
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Marital Status</Typography>
            <div className="flex flex-1 flex-col">
              {" "}
              <Select
                name="maritalStatus"
                options={maritalStatusOptions}
                onChange={formikOfficer.handleChange}
                value={formikOfficer?.values?.maritalStatus || ""}
                error={!!formikOfficer.errors.maritalStatus && formikOfficer.touched.maritalStatus}
                errorText={formikOfficer.errors.maritalStatus}
                onBlur={formikOfficer.handleBlur}
                required
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Address</Typography>
            <div className="flex flex-1 flex-col">
              {" "}
              <TextArea
                className="w-full border-primary"
                placeholder="Enter Address"
                name="address"
                onChange={formikOfficer.handleChange}
                value={formikOfficer.values.address || ""}
                error={!!formikOfficer.errors.address && formikOfficer.touched.address}
                errorText={formikOfficer.errors.address}
                onBlur={formikOfficer.handleBlur}
              />
            </div>
          </div>
          <div className="flex flex-1 flex-row items-center space-x-2">
            <Typography className="min-w-36">Signatory Officer</Typography>
            <CheckBox
              name="signatory"
              onChange={formikOfficer.handleChange}
              onClick={() => formikOfficer.setFieldValue("signatory", true)}
              checked={formikOfficer.values.signatory ?? false}
              error={!!formikOfficer.errors.signatory && formikOfficer.touched.signatory}
              errorText={formikOfficer.errors.signatory}
            />
          </div>
          <div className="flex flex-1 justify-end space-x-2 mt-4">
            <button className="btn bg-zinc-200 text-primary w-32" onClick={toggleModal}>
              Cancel
            </button>
            <button type="submit" className="btn bg-primary text-white w-32 hover:text-primary">
              Save
            </button>
          </div>
        </div>
      </Form>
    </FormikProvider>
  );
};

export default CoopOfficerForm;

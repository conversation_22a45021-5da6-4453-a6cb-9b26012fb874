
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import Button from "@components/common/Button";

//For the chart
import { <PERSON>, Pie<PERSON>hart, ResponsiveContainer, Tooltip as RechartsTooltip, Cell, PieLabelRenderProps } from 'recharts';
import { Chart as ChartJS, CategoryScale, ArcElement, LinearScale, BarElement, Title, Tooltip, Legend } from "chart.js";

//Images and Icons are stored here.
import { FiChevronRight } from "react-icons/fi";
import dashboard from "@assets/dashboard/male_user.gif";
import ColumnChart from "../IncomingOutgoingCashierDashboard.tsx/charts/columnChart";

// Register the components you plan to use
ChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, Title, Tooltip, Legend);

const SalesDashboard: React.FC = () => {
  //For getting the user data
  const user = useSelector((state: RootState) => state.auth?.user?.data);

  //Dummy data 
  const data = {
    labels: ["Jan", "Feb", "March", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    datasets: [
      {
        label: "Registered Cooperatives 2024",
        data: [30, 21, 29, 40, 9, 20, 31, 50, 12, 31, 32, 25],
        backgroundColor: "#1213eb",
        borderRadius: 3,
        barThickness: 30, // Sets the exact width of each bar in pixels
        maxBarThickness: 40, // Sets the maximum width of the bar
        categoryPercentage: 0.8, // Controls the width of the category relative to the entire bar chart
        barPercentage: 0.9, // Controls the width of the bars relative to the category width
      },
    ],
  };

  const columnCategories = data.labels;
  const columnSeries = data.datasets.map(ds => ({ name: ds.label, data: ds.data }));
  const columnColors = data.datasets.map(ds =>
    Array.isArray(ds.backgroundColor) ? (ds.backgroundColor[0] as string) : (ds.backgroundColor as string || "#1213eb")
  );

  //For the pie chart
  const COLORS = ['#FFD000', '#042882', '#28A845', '#6A31FF'];
  const data2 = [
    { name: 'Life Products', value: 500 },
    { name: 'Non-Life Products', value: 500 },
    { name: 'Micro Life Products', value: 200 },
    { name: 'Micro Non-Life Products', value: 300 },
  ];

  const renderLabel = (props: PieLabelRenderProps) => {
    const { cx, cy, midAngle, outerRadius, name, value } = props;
    if (typeof cx !== 'number' || typeof cy !== 'number' || typeof midAngle !== 'number' || typeof outerRadius !== 'number') {
      return null;
    }
    const r = outerRadius + 45; // Increased distance
    const x = cx + r * Math.cos(-midAngle * (Math.PI / 180));
    const y = cy + r * Math.sin(-midAngle * (Math.PI / 180));
    
    // Custom splitting for your specific product names
    const splitName = (name: string) => {
      switch (name) {
        case 'Life Products':
          return ['Life', 'Products'];
        case 'Non-Life Products':
          return ['Non-Life', 'Products'];
        case 'Micro Life Products':
          return ['Micro Life', 'Products'];
        case 'Micro Non-Life Products':
          return ['Micro', 'Non-Life', 'Products'];
        default:
          return [name];
      }
    };

    const nameLines = splitName(name);
    
    return (
      <text x={x} y={y} fill="#000" textAnchor={x > cx ? 'start' : 'end'} style={{ fontSize: '11px', fontWeight: 'bold' }}>
        {nameLines.map((line, index) => (
          <tspan key={index} x={x} dy={index === 0 ? -8 : 12}>
            {line}
          </tspan>
        ))}
        <tspan x={x} dy="12">{value}</tspan>
      </text>
    );
  };

  return (
    <div className="p-5">
      <div className="w-full flex items-start justify-between gap-6 px-5">
        <div className="flex-1 flex flex-col items-start">
          <span className="text-primary xl:text-4xl text-xl font-semibold">Welcome back, {user?.firstname}</span>
          <span className="xl:text-sm text-xs text-gray-700">Have a nice day at work.</span>
        </div>
        <div className="relative self-start ml-8 w-[20rem] h-[320px] overflow-hidden">
          <img src={dashboard} className="absolute inset-0 w-full h-full object-cover transform scale-[1.15] translate-y-[-12%] translate-x-[5%]" />
        </div>
      </div>
      <br />
      <div className="w-full flex py-5 text-2xl font-semibold translate-y-[-25%]">
        Overview
      </div>
      {/* PARENT SA MGA CHARTS */}
      <div className="w-full h-1/2">
        <div className="w-full h-full flex gap-4">
          {" "}
          <div className="w-2/3 h-full rounded-lg border border-zinc-100 shadow-md p-5"> 
            <div className="w-full flex justify-between items-center uppercase font-semibold text-zinc-500 p-5">
              Registered Cooperatives
            </div>
             <ColumnChart 
                scrollbar={true} 
                categories={columnCategories} 
                series={columnSeries} 
                colors={columnColors} 
                useGradient 
                title=" " 
                percentage={true}
                hideToolbar={true}
              />
          </div>
          <div className="w-1/3 h-80 rounded-lg border border-zinc-100 shadow-md p-5">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={data2}
                  dataKey="value"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  paddingAngle={3}
                  label={renderLabel}
                  labelLine={true}
                >
                  {data2.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <RechartsTooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      <div className="h-1/4 w-full flex flex-col mt-10">
        <div className="mb-4 xl:text-xl text-sm flex justify-between items-center px-10">
          <div>Continue where you left off ...</div>
          <div className="underline text-sm text-primary cursor-pointer flex items-center justify-center">
            View all drafts <FiChevronRight size={20} />
          </div>
        </div>

        <div className="w-full flex flex-col items-center justify-between gap-2 ">
          <div className=" border-b border-zinc-300 xl:p-6 p-2 w-full rounded-md flex items-center justify-center text-sm">
            <div className=" w-1/5 flex items-center justify-center">January 11, 2024</div>
            <div className=" w-1/5 flex items-center justify-center ">Product Guidelines</div>
            <div className=" w-1/5 flex items-center justify-center">Customized</div>
            <div className=" w-1/5 flex items-center justify-center ">
              <div className="px-4 p-2 flex items-center justify-center rounded-full bg-zinc-200  ">Draft</div>
            </div>
            <div className=" w-1/5 flex items-center justify-center ">
              <Button classNames="btn px-8 p-2 flex items-center justify-center rounded-md bg-primary  text-white">View Details</Button>
            </div>
          </div>

          <div className=" border-b border-zinc-300 xl:p-6 p-2 w-full rounded-md flex items-center justify-center text-sm">
            <div className=" w-1/5 flex items-center justify-center">January 11, 2024</div>
            <div className=" w-1/5 flex items-center justify-center ">Product Guidelines</div>
            <div className=" w-1/5 flex items-center justify-center">Standard</div>
            <div className=" w-1/5 flex items-center justify-center ">
              <div className="px-4 p-2 flex items-center justify-center rounded-full bg-zinc-200  ">Draft</div>
            </div>
            <div className=" w-1/5 flex items-center justify-center ">
              <Button classNames="btn px-8 p-2 flex items-center justify-center rounded-md bg-primary  text-white">View Details</Button>
            </div>
          </div>

          <div className=" border-b border-zinc-300 xl:p-6 p-2 w-full rounded-md flex items-center justify-center text-sm">
            <div className=" w-1/5 flex items-center justify-center">January 11, 2024</div>
            <div className=" w-1/5 flex items-center justify-center ">Product Guidelines</div>
            <div className=" w-1/5 flex items-center justify-center">Customized</div>
            <div className=" w-1/5 flex items-center justify-center ">
              <div className="px-4 p-2 flex items-center justify-center rounded-full bg-zinc-200  ">Draft</div>
            </div>
            <div className=" w-1/5 flex items-center justify-center ">
              <Button classNames="btn px-8 p-2 flex items-center justify-center rounded-md bg-primary  text-white">View Details</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesDashboard;

{"name": "ccis-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview --port 3000", "auto-commit-push": "git add . && git commit -m \"$MSG\" && git push origin $(git rev-parse --abbrev-ref HEAD)", "patrick": "npm run build && npm run auto-commit-push"}, "dependencies": {"@lexical/react": "^0.16.1", "@lottiefiles/dotlottie-web": "^0.49.0", "@reduxjs/toolkit": "^2.2.5", "@types/chart.js": "^2.9.41", "@types/lodash": "^4.17.4", "apexcharts": "^4.5.0", "axios": "^1.7.2", "chart.js": "^4.4.4", "classnames": "^2.5.1", "date-fns": "^3.6.0", "dayjs": "^1.11.12", "flatlist-react": "^1.5.14", "formik": "^2.4.6", "headless-stepper": "^1.11.0", "immer": "^10.1.1", "laravel-echo": "^2.0.2", "lexical": "^0.16.1", "lottie-react": "^2.4.1", "lucide-react": "^0.381.0", "pusher-js": "^8.4.0", "quill-image-resize-module-react": "^3.0.0", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.2.0", "react-data-table-component": "^7.6.2", "react-datepicker": "^7.3.0", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-icons": "^5.2.1", "react-loader-spinner": "^6.1.6", "react-modern-drawer": "^1.3.1", "react-pdf": "^9.1.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-signature-pad-wrapper": "^4.0.1", "react-to-print": "^3.0.5", "react-toastify": "^10.0.5", "recharts": "^3.2.1", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0", "styled-components": "^6.1.11", "sweetalert2": "^11.6.13", "use-debounce": "^10.0.1", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "devDependencies": {"@types/node": "^20.14.2", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/redux-logger": "^3.0.13", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "daisyui": "^4.11.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "prettier": "^3.6.2", "tailwindcss": "^3.4.3", "typescript": "^5.2.2", "vite": "^5.2.0"}, "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "postcss.config.js", "repository": {"type": "git", "url": "git+https://<EMAIL>/ccis1/ccis-fe.git"}, "author": "", "license": "ISC", "homepage": "https://bitbucket.org/ccis1/ccis-fe#readme"}